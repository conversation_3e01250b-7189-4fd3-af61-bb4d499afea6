import os
import logging
from datetime import datetime


def setup_logger(name):
    # 获取当前日期，格式为YYYYMMDD
    current_date = datetime.now().strftime("%Y%m%d")

    # 创建日志文件名
    log_filename = f"{name}_{current_date}.log"

    # 创建日志目录路径（当前工作目录/data/logs/）
    log_dir = os.path.join(os.getcwd(), "data", "logs")

    # 如果目录不存在则创建
    os.makedirs(log_dir, exist_ok=True)

    # 完整的日志文件路径
    log_filepath = os.path.join(log_dir, log_filename)

    # 创建logger
    logger = logging.getLogger(name)
    logger.setLevel(logging.DEBUG)  # 设置最低日志级别

    # 创建formatter，并设置时间格式为 2025-07-16 12:01:33
    formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'  # 指定时间格式
    )

    # 创建文件handler并设置级别为DEBUG，追加模式
    file_handler = logging.FileHandler(log_filepath, mode='a')
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(formatter)

    # 将handler添加到logger
    logger.addHandler(file_handler)

    return logger


# 使用示例
if __name__ == "__main__":
    # 初始化logger，传入name参数
    my_logger = setup_logger("my_app")

    # 记录不同级别的日志
    my_logger.debug("This is a debug message")
    my_logger.info("This is an info message")
    my_logger.warning("This is a warning message")
    my_logger.error("This is an error message")
    my_logger.critical("This is a critical message")