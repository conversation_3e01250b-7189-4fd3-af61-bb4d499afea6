# 服务器基本配置信息
# 除 log_verbose/HTTPX_DEFAULT_TIMEOUT 修改后即时生效
# 其它配置项修改后都需要重启服务器才能生效，服务运行期间请勿修改


# 生成该配置模板的项目代码版本，如这里的值与程序实际版本不一致，建议重建配置文件模板
version: 0.3.1.3

# 是否开启日志详细信息
log_verbose: false

# httpx 请求默认超时时间（秒）。如果加载模型或对话较慢，出现超时错误，可以适当加大该值。
HTTPX_DEFAULT_TIMEOUT: 300.0

# 知识库默认存储路径
KB_ROOT_PATH: data\knowledge_base

# 数据库默认存储路径。如果使用sqlite，可以直接修改DB_ROOT_PATH；如果使用其它数据库，请直接修改SQLALCHEMY_DATABASE_URI。
DB_ROOT_PATH: 
  data\knowledge_base\info.db

# 知识库信息数据库连接URI
SQLALCHEMY_DATABASE_URI: 
  sqlite:///data\knowledge_base\info.db

# API 是否开启跨域
OPEN_CROSS_DOMAIN: false

# 中台host
MIDDLE_PLATFORM_HOST: http://**************:2323

# jwt key 必须和中台的key 一致
JWT_KEY: yinxin_2233100122

# 各服务器默认绑定host。如改为"0.0.0.0"需要修改下方所有XX_SERVER的host
# Windows 下 WEBUI 自动弹出浏览器时，如果地址为 "0.0.0.0" 是无法访问的，需要手动修改地址栏
DEFAULT_BIND_HOST: 127.0.0.1

# API 服务器地址。其中 public_host 用于生成云服务公网访问链接（如知识库文档链接）
API_SERVER:
  host: 0.0.0.0
  port: 7861
  public_host: 127.0.0.1
  public_port: 7861

# WEBUI 服务器地址
WEBUI_SERVER:
  host: 127.0.0.1
  port: 8501


#mysql
MYSQL:
  host: 127.0.0.1
  port: 3306
  user: root
  password: root