from typing import Optional
from chatchat.server.db.base import Base
from sqlalchemy import Index, Integer, LargeBinary, Numeric, REAL, Text, text
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column
import decimal


class BusinessInfo(Base):
    __tablename__ = 'business_info'

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    is_delete: Mapped[int] = mapped_column(Integer, server_default=text('0'))
    add_time: Mapped[int] = mapped_column(Integer)
    update_time: Mapped[int] = mapped_column(Integer)
    business_name: Mapped[Optional[str]] = mapped_column(Text)
    user_name: Mapped[Optional[str]] = mapped_column(Text)
    register_time: Mapped[Optional[int]] = mapped_column(Integer)
    last_login_time: Mapped[Optional[int]] = mapped_column(Integer)


class ChatHistory(Base):
    __tablename__ = 'chat_history'

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    query: Mapped[Optional[str]] = mapped_column(Text)
    mode: Mapped[Optional[str]] = mapped_column(Text)
    knowledge_base: Mapped[Optional[str]] = mapped_column(Text)
    history: Mapped[Optional[str]] = mapped_column(Text)
    model: Mapped[Optional[str]] = mapped_column(Text)
    prompt_name: Mapped[Optional[str]] = mapped_column(Text)
    answer: Mapped[Optional[str]] = mapped_column(Text)
    add_time: Mapped[Optional[int]] = mapped_column(Integer)
    update_time: Mapped[Optional[int]] = mapped_column(Integer)
    source_documents: Mapped[Optional[str]] = mapped_column(Text)
    is_update_knowledge: Mapped[Optional[bytes]] = mapped_column(LargeBinary)
    add_type: Mapped[Optional[str]] = mapped_column(Text)
    view_id: Mapped[Optional[str]] = mapped_column(Text)


class PlatformInfo(Base):
    __tablename__ = 'platform_info'
    __table_args__ = (
        Index('name', 'platform_name', unique=True),
    )

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    business_id: Mapped[str] = mapped_column(Text)
    platform_name: Mapped[str] = mapped_column(Text)
    platform_type: Mapped[str] = mapped_column(Text)
    api_base_url: Mapped[str] = mapped_column(Text)
    api_key: Mapped[str] = mapped_column(Text)
    add_time: Mapped[int] = mapped_column(Integer)
    update_time: Mapped[int] = mapped_column(Integer)
    api_proxy: Mapped[Optional[str]] = mapped_column(Text)
    api_concurrencies: Mapped[Optional[int]] = mapped_column(Integer, server_default=text('1'))


class PlatformModel(Base):
    __tablename__ = 'platform_model'
    __table_args__ = (
        Index('model info', 'model_type', 'model_name', unique=True),
    )

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    platform_id: Mapped[int] = mapped_column(Integer)
    model_type: Mapped[str] = mapped_column(Text)
    model_code: Mapped[str] = mapped_column(Text)
    add_time: Mapped[int] = mapped_column(Integer)
    update_time: Mapped[int] = mapped_column(Integer)
    model_name: Mapped[Optional[str]] = mapped_column(Text)
    description: Mapped[Optional[str]] = mapped_column(Text)
    context_length: Mapped[Optional[decimal.Decimal]] = mapped_column(Numeric)
    QPM: Mapped[Optional[int]] = mapped_column(Integer)
    TPM: Mapped[Optional[int]] = mapped_column(Integer)
    is_set: Mapped[Optional[int]] = mapped_column(Integer)


class ProductChoices(Base):
    __tablename__ = 'product_choices'

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    product_id: Mapped[Optional[int]] = mapped_column(Integer)
    standard: Mapped[Optional[str]] = mapped_column(Text)
    description: Mapped[Optional[str]] = mapped_column(Text)
    amount: Mapped[Optional[int]] = mapped_column(Integer)
    single_price: Mapped[Optional[float]] = mapped_column(REAL)
    group_price: Mapped[Optional[float]] = mapped_column(REAL)
    reference_price: Mapped[Optional[float]] = mapped_column(REAL)
    image: Mapped[Optional[str]] = mapped_column(Text)
    is_delete: Mapped[Optional[int]] = mapped_column(Integer, server_default=text('0'))
    add_time: Mapped[Optional[int]] = mapped_column(Integer)
    update_time: Mapped[Optional[int]] = mapped_column(Integer)


class ProductInfo(Base):
    __tablename__ = 'product_info'

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    platform: Mapped[Optional[str]] = mapped_column(Text)
    shop_name: Mapped[Optional[str]] = mapped_column(Text)
    platform_shop_id: Mapped[Optional[int]] = mapped_column(Integer)
    product_id: Mapped[Optional[int]] = mapped_column(Integer)
    product_type: Mapped[Optional[str]] = mapped_column(Text)
    images: Mapped[Optional[str]] = mapped_column(Text)
    description: Mapped[Optional[str]] = mapped_column(Text)
    share_text: Mapped[Optional[str]] = mapped_column(Text)
    attrs: Mapped[Optional[str]] = mapped_column(Text)
    url: Mapped[Optional[str]] = mapped_column(Text)
    is_update_knowledge: Mapped[Optional[int]] = mapped_column(Integer, server_default=text('0'))
    is_delete: Mapped[Optional[int]] = mapped_column(Integer, server_default=text('0'))
    add_time: Mapped[Optional[int]] = mapped_column(Integer)
    update_time: Mapped[Optional[int]] = mapped_column(Integer)
    product_name: Mapped[Optional[str]] = mapped_column(Text)


class PromptFormat(Base):
    __tablename__ = 'prompt_format'
    __table_args__ = (
        Index('prompt', 'prompt_type', 'prompt_name'),
    )

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    prompt_type: Mapped[Optional[str]] = mapped_column(Text)
    prompt_name: Mapped[Optional[str]] = mapped_column(Text)
    template: Mapped[Optional[str]] = mapped_column(Text)
    description: Mapped[Optional[str]] = mapped_column(Text)
    add_time: Mapped[Optional[int]] = mapped_column(Integer)
    update_time: Mapped[Optional[int]] = mapped_column(Integer)
    is_delete: Mapped[Optional[bytes]] = mapped_column(LargeBinary)


class ShopInfo(Base):
    __tablename__ = 'shop_info'
    __table_args__ = (
        Index('shop_knowledg', 'shop_name', 'knowledge_base'),
        Index('union', 'platform_shop_id', unique=True)
    )

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    business_id: Mapped[str] = mapped_column(Text)
    view_id: Mapped[str] = mapped_column(Text)
    shop_name: Mapped[str] = mapped_column(Text)
    is_delete: Mapped[int] = mapped_column(Integer, server_default=text('0'))
    knowledge_base: Mapped[Optional[str]] = mapped_column(Text)
    prompt_name: Mapped[Optional[str]] = mapped_column(Text)
    description: Mapped[Optional[str]] = mapped_column(Text)
    add_time: Mapped[Optional[int]] = mapped_column(Integer)
    update_time: Mapped[Optional[int]] = mapped_column(Integer)
    collectionState: Mapped[Optional[decimal.Decimal]] = mapped_column(Numeric, server_default=text('0'))
    platform: Mapped[Optional[str]] = mapped_column(Text)
    platform_shop_id: Mapped[Optional[str]] = mapped_column(Text)


class UserInfo(Base):
    __tablename__ = 'user_info'
    __table_args__ = (
        Index('account', 'account'),
    )

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    account: Mapped[str] = mapped_column(Text)
    password: Mapped[str] = mapped_column(Text)
    user_name: Mapped[Optional[str]] = mapped_column(Text)
    phone: Mapped[Optional[str]] = mapped_column(Text)
    login_time: Mapped[Optional[str]] = mapped_column(Text)
    session_time: Mapped[Optional[int]] = mapped_column(Integer)
    add_time: Mapped[Optional[int]] = mapped_column(Integer)
    update_time: Mapped[Optional[int]] = mapped_column(Integer)
