2025-07-30 17:53:46.464 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '1/vector_store/doubao-embedding-text-240715' from disk.
2025-07-30 17:54:07.201 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '抖店_夕玲珠宝批发百货/vector_store/doubao-embedding-text-240715' from disk.
2025-07-30 17:54:38.994 | INFO     | chatchat.server.knowledge_base.utils:file2docs:375 - UnstructuredFileLoader used for D:\客服系统\server_customer_service\data\knowledge_base\抖店_夕玲珠宝批发百货\content\抖店_夕玲珠宝批发百货_产品.txt
2025-07-30 17:54:44.055 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:save:40 - 已将向量库 ('抖店_夕玲珠宝批发百货', 'doubao-embedding-text-240715') 保存到磁盘
2025-07-30 18:18:05.408 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '抖店1_1夕玲珠宝批发百货/vector_store/doubao-embedding-text-240715' from disk.
2025-07-30 20:10:10.672 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-30 20:10:10.673 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-30 20:10:11.287 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-30 20:10:11.296 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-30 20:10:12.276 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-30 20:10:16.452 | ERROR    | chatchat.webui_pages.utils:get:64 - ConnectError: error when get /knowledge_base/list_knowledge_bases: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-30 20:10:17.678 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-30 20:44:45.962 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '抖店_夕玲珠宝批发百货/vector_store/doubao-embedding-text-240715' from disk.
2025-07-30 20:44:48.361 | ERROR    | chatchat.server.chat.utils:parse_open_error:89 - 错误信息:错误类型:invalid_api_key, 信息:Incorrect API key provided. 
2025-07-30 20:44:48.398 | ERROR    | chatchat.server.utils:wrap_done:48 - AuthenticationError: Caught exception: Error code: 401 - {'error': {'message': 'Incorrect API key provided. ', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}, 'request_id': '22bfbdf8-cb2f-9d85-bad5-94012bf581e0'}
2025-07-30 20:45:35.850 | ERROR    | chatchat.server.chat.utils:parse_open_error:89 - 错误信息:错误类型:invalid_api_key, 信息:Incorrect API key provided. 
2025-07-30 20:45:35.858 | ERROR    | chatchat.server.utils:wrap_done:48 - AuthenticationError: Caught exception: Error code: 401 - {'error': {'message': 'Incorrect API key provided. ', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}, 'request_id': 'b4e5868f-9482-9306-82c1-bafae0a356c4'}
2025-07-30 20:46:19.537 | ERROR    | chatchat.server.chat.utils:parse_open_error:89 - 错误信息:错误类型:invalid_api_key, 信息:Incorrect API key provided. 
2025-07-30 20:46:19.547 | ERROR    | chatchat.server.utils:wrap_done:48 - AuthenticationError: Caught exception: Error code: 401 - {'error': {'message': 'Incorrect API key provided. ', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}, 'request_id': '35af7af4-7342-9818-80b4-456d55a3b3e9'}
2025-07-30 21:03:51.983 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '1/vector_store/doubao-embedding-text-240715' from disk.
2025-07-30 21:04:04.495 | INFO     | chatchat.server.knowledge_base.utils:file2docs:375 - UnstructuredFileLoader used for E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\knowledge_base\1\content\抖店_夕玲珠宝批发百货_产品.txt
2025-07-30 21:04:10.884 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:save:40 - 已将向量库 ('1', 'doubao-embedding-text-240715') 保存到磁盘
2025-07-30 21:04:28.374 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '2/vector_store/text-embedding-v3' from disk.
2025-07-30 21:04:36.069 | INFO     | chatchat.server.knowledge_base.utils:file2docs:375 - UnstructuredFileLoader used for E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\knowledge_base\2\content\抖店_夕玲珠宝批发百货_产品.txt
2025-07-30 21:04:41.424 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:save:40 - 已将向量库 ('2', 'text-embedding-v3') 保存到磁盘
2025-07-30 21:13:29.662 | ERROR    | chatchat.webui_pages.utils:to_json:233 - JSONDecodeError: API未能返回正确的JSON。Expecting value: line 1 column 1 (char 0)
2025-07-31 09:37:21.204 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-31 09:37:21.212 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-31 09:38:33.597 | ERROR    | chatchat.webui_pages.utils:to_json:233 - JSONDecodeError: API未能返回正确的JSON。Expecting value: line 1 column 1 (char 0)
2025-07-31 10:03:51.954 | ERROR    | chatchat.webui_pages.utils:to_json:233 - JSONDecodeError: API未能返回正确的JSON。Expecting value: line 1 column 1 (char 0)
2025-07-31 10:17:56.431 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-31 10:17:56.437 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-31 10:37:16.548 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-31 10:37:16.556 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 10:37:23.050 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-31 10:38:03.287 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-31 10:38:03.287 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-31 10:44:10.971 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-31 10:44:10.979 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 10:44:13.596 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-31 10:44:13.597 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-31 11:05:33.144 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-31 11:05:33.145 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 11:05:34.857 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-31 11:05:55.674 | ERROR    | chatchat.webui_pages.utils:to_json:233 - JSONDecodeError: API未能返回正确的JSON。Expecting value: line 1 column 1 (char 0)
2025-07-31 11:08:10.421 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-31 11:08:10.421 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-31 11:08:13.762 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-31 11:08:13.765 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 11:08:15.654 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-31 11:10:27.720 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-31 11:10:27.741 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 11:10:30.989 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-31 11:10:30.989 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-31 11:13:59.954 | ERROR    | chatchat.webui_pages.utils:post:87 - ConnectError: error when post /server/get_prompt_template_list: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-31 11:14:00.109 | ERROR    | chatchat.webui_pages.utils:post:87 - ConnectError: error when post /server/get_prompt_template_list: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-31 11:14:08.704 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '3/vector_store/doubao-embedding-text-240715' from disk.
2025-07-31 11:14:09.238 | ERROR    | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:140 - Could not import faiss python package. Please install it with `pip install faiss-gpu` (for CUDA supported GPU) or `pip install faiss-cpu` (depending on Python version).
2025-07-31 11:14:09.238 | ERROR    | chatchat.server.knowledge_base.kb_api:create_kb:45 - RuntimeError: 创建知识库出错： 向量库 3 加载失败。
2025-07-31 11:14:31.606 | ERROR    | chatchat.webui_pages.utils:to_json:233 - JSONDecodeError: API未能返回正确的JSON。Expecting value: line 1 column 1 (char 0)
2025-07-31 11:25:17.730 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-31 11:25:17.731 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-31 11:37:24.932 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-31 11:37:24.939 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 11:37:28.432 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-31 11:37:36.139 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-31 11:37:36.145 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 11:38:42.315 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-31 11:38:42.316 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-31 11:38:52.132 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-31 11:38:52.139 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 11:38:55.671 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-31 11:40:12.174 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-31 11:40:12.174 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-31 11:40:22.138 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-31 11:40:22.146 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 11:40:26.078 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-31 11:40:31.234 | ERROR    | chatchat.webui_pages.utils:get:64 - ConnectError: error when get /knowledge_base/list_knowledge_bases: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-31 11:40:33.042 | ERROR    | chatchat.webui_pages.utils:get:64 - ConnectError: error when get /knowledge_base/list_knowledge_bases: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-31 11:40:33.264 | ERROR    | chatchat.webui_pages.utils:get:64 - ConnectError: error when get /knowledge_base/list_knowledge_bases: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-31 11:40:35.086 | ERROR    | chatchat.webui_pages.utils:get:64 - ConnectError: error when get /knowledge_base/list_knowledge_bases: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-31 11:40:35.302 | ERROR    | chatchat.webui_pages.utils:get:64 - ConnectError: error when get /knowledge_base/list_knowledge_bases: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-31 11:40:35.302 | ERROR    | chatchat.webui_pages.utils:to_json:233 - AttributeError: API未能返回正确的JSON。'NoneType' object has no attribute 'json'
2025-07-31 11:40:35.751 | ERROR    | chatchat.webui_pages.utils:post:87 - ConnectError: error when post /chat/history: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-31 11:40:35.925 | ERROR    | chatchat.webui_pages.utils:post:87 - ConnectError: error when post /chat/history: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-31 11:40:37.123 | ERROR    | chatchat.webui_pages.utils:get:64 - ConnectError: error when get /knowledge_base/list_knowledge_bases: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-31 11:40:37.124 | ERROR    | chatchat.webui_pages.utils:to_json:233 - AttributeError: API未能返回正确的JSON。'NoneType' object has no attribute 'json'
2025-07-31 11:40:37.452 | ERROR    | chatchat.webui_pages.utils:post:87 - ConnectError: error when post /server/get_prompt_template_list: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-31 11:40:37.499 | ERROR    | chatchat.webui_pages.utils:post:87 - ConnectError: error when post /server/get_prompt_template_list: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-31 11:40:37.793 | ERROR    | chatchat.webui_pages.utils:post:87 - ConnectError: error when post /chat/history: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-31 11:40:37.982 | ERROR    | chatchat.webui_pages.utils:post:87 - ConnectError: error when post /chat/history: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-31 11:40:39.486 | ERROR    | chatchat.webui_pages.utils:post:87 - ConnectError: error when post /server/get_prompt_template_list: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-31 11:40:39.532 | ERROR    | chatchat.webui_pages.utils:post:87 - ConnectError: error when post /server/get_prompt_template_list: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-31 11:40:39.819 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-31 11:40:39.820 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-31 11:40:39.842 | ERROR    | chatchat.webui_pages.utils:post:87 - ConnectError: error when post /chat/history: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-31 11:40:39.842 | ERROR    | chatchat.webui_pages.utils:to_json:233 - AttributeError: API未能返回正确的JSON。'NoneType' object has no attribute 'json'
2025-07-31 11:40:40.014 | ERROR    | chatchat.webui_pages.utils:post:87 - ConnectError: error when post /chat/history: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-31 11:40:40.015 | ERROR    | chatchat.webui_pages.utils:to_json:233 - AttributeError: API未能返回正确的JSON。'NoneType' object has no attribute 'json'
2025-07-31 11:40:41.543 | ERROR    | chatchat.webui_pages.utils:post:87 - ConnectError: error when post /server/get_prompt_template_list: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-31 11:40:41.543 | ERROR    | chatchat.webui_pages.utils:to_json:233 - AttributeError: API未能返回正确的JSON。'NoneType' object has no attribute 'json'
2025-07-31 11:40:41.640 | ERROR    | chatchat.webui_pages.utils:post:87 - ConnectError: error when post /server/get_prompt_template_list: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-31 11:40:41.641 | ERROR    | chatchat.webui_pages.utils:to_json:233 - AttributeError: API未能返回正确的JSON。'NoneType' object has no attribute 'json'
2025-07-31 11:40:47.076 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-31 11:40:47.081 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 11:40:52.913 | ERROR    | chatchat.webui_pages.utils:get:64 - ConnectError: error when get /knowledge_base/list_knowledge_bases: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-31 11:40:54.966 | ERROR    | chatchat.webui_pages.utils:get:64 - ConnectError: error when get /knowledge_base/list_knowledge_bases: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-31 11:40:56.689 | ERROR    | chatchat.webui_pages.utils:post:87 - ConnectError: error when post /chat/history: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-31 11:40:56.747 | ERROR    | chatchat.webui_pages.utils:post:87 - ConnectError: error when post /chat/history: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-31 11:40:57.003 | ERROR    | chatchat.webui_pages.utils:get:64 - ConnectError: error when get /knowledge_base/list_knowledge_bases: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-31 11:40:57.005 | ERROR    | chatchat.webui_pages.utils:to_json:233 - AttributeError: API未能返回正确的JSON。'NoneType' object has no attribute 'json'
2025-07-31 11:40:57.449 | ERROR    | chatchat.webui_pages.utils:post:87 - ConnectError: error when post /server/get_prompt_template_list: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-31 11:40:57.591 | ERROR    | chatchat.webui_pages.utils:post:87 - ConnectError: error when post /server/get_prompt_template_list: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-31 11:40:58.721 | ERROR    | chatchat.webui_pages.utils:post:87 - ConnectError: error when post /chat/history: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-31 11:40:58.777 | ERROR    | chatchat.webui_pages.utils:post:87 - ConnectError: error when post /chat/history: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-31 11:40:59.472 | ERROR    | chatchat.webui_pages.utils:post:87 - ConnectError: error when post /server/get_prompt_template_list: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-31 11:40:59.635 | ERROR    | chatchat.webui_pages.utils:post:87 - ConnectError: error when post /server/get_prompt_template_list: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-31 11:41:00.742 | ERROR    | chatchat.webui_pages.utils:post:87 - ConnectError: error when post /chat/history: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-31 11:41:00.742 | ERROR    | chatchat.webui_pages.utils:to_json:233 - AttributeError: API未能返回正确的JSON。'NoneType' object has no attribute 'json'
2025-07-31 11:41:00.814 | ERROR    | chatchat.webui_pages.utils:post:87 - ConnectError: error when post /chat/history: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-31 11:41:00.818 | ERROR    | chatchat.webui_pages.utils:to_json:233 - AttributeError: API未能返回正确的JSON。'NoneType' object has no attribute 'json'
2025-07-31 11:41:01.524 | ERROR    | chatchat.webui_pages.utils:post:87 - ConnectError: error when post /server/get_prompt_template_list: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-31 11:41:01.524 | ERROR    | chatchat.webui_pages.utils:to_json:233 - AttributeError: API未能返回正确的JSON。'NoneType' object has no attribute 'json'
2025-07-31 11:41:01.658 | ERROR    | chatchat.webui_pages.utils:post:87 - ConnectError: error when post /server/get_prompt_template_list: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-31 11:41:01.658 | ERROR    | chatchat.webui_pages.utils:to_json:233 - AttributeError: API未能返回正确的JSON。'NoneType' object has no attribute 'json'
2025-07-31 11:45:39.740 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-31 11:45:39.740 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-31 11:45:39.740 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-31 11:45:39.741 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-31 11:51:55.818 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-31 11:51:55.825 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 11:51:58.366 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-31 11:51:58.373 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 11:52:01.850 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-31 11:52:01.912 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-31 11:52:38.771 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-31 11:52:38.773 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-31 11:52:44.969 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-31 11:52:44.976 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 11:52:48.084 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-31 11:56:03.275 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-31 11:56:03.276 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-31 11:56:09.845 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-31 11:56:09.857 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 11:56:13.133 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-31 12:03:00.197 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-31 12:03:00.199 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-31 12:03:06.681 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-31 12:03:06.689 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 12:03:09.738 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-31 12:04:45.601 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-31 12:04:45.601 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-31 12:04:52.333 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-31 12:04:52.340 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 12:04:55.557 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-31 12:07:25.904 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-31 12:07:25.904 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-31 12:07:32.311 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-31 12:07:32.318 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 12:07:35.406 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-31 12:09:08.805 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-31 12:09:08.805 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-31 12:09:15.018 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-31 12:09:15.028 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 12:09:18.050 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-31 14:04:42.879 | ERROR    | chatchat.webui_pages.utils:to_json:233 - JSONDecodeError: API未能返回正确的JSON。Expecting value: line 1 column 1 (char 0)
2025-07-31 14:08:12.579 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-31 14:08:12.584 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-31 14:08:19.753 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-31 14:08:19.762 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 14:08:25.612 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-31 14:08:47.961 | ERROR    | chatchat.webui_pages.utils:to_json:233 - JSONDecodeError: API未能返回正确的JSON。Expecting value: line 1 column 1 (char 0)
2025-07-31 14:13:17.928 | ERROR    | chatchat.webui_pages.utils:to_json:233 - JSONDecodeError: API未能返回正确的JSON。Expecting value: line 1 column 1 (char 0)
2025-07-31 14:14:13.280 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-31 14:14:13.280 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-31 14:14:19.480 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-31 14:14:19.496 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 14:14:25.007 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-31 14:14:59.479 | ERROR    | chatchat.webui_pages.utils:to_json:233 - JSONDecodeError: API未能返回正确的JSON。Expecting value: line 1 column 1 (char 0)
2025-07-31 14:18:54.494 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-31 14:18:54.495 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-31 14:19:00.948 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-31 14:19:00.955 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 14:19:06.420 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-31 14:19:09.992 | ERROR    | chatchat.webui_pages.utils:to_json:233 - JSONDecodeError: API未能返回正确的JSON。Expecting value: line 1 column 1 (char 0)
2025-07-31 14:20:02.999 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-31 14:20:03.000 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-31 14:20:09.282 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-31 14:20:09.290 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 14:20:14.680 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-31 14:20:19.858 | ERROR    | chatchat.webui_pages.utils:to_json:233 - JSONDecodeError: API未能返回正确的JSON。Expecting value: line 1 column 1 (char 0)
2025-07-31 14:20:57.191 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-31 14:20:57.191 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-31 14:21:03.770 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-31 14:21:03.776 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 14:21:09.045 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-31 14:21:20.346 | ERROR    | chatchat.webui_pages.utils:to_json:233 - JSONDecodeError: API未能返回正确的JSON。Expecting value: line 1 column 1 (char 0)
2025-07-31 14:22:29.535 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-31 14:22:29.536 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-31 14:22:33.790 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-31 14:22:33.792 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-31 14:22:36.071 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-31 14:22:36.079 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 14:22:40.860 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-31 14:22:40.868 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 14:22:42.199 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-31 14:22:44.094 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-31 14:23:17.075 | ERROR    | chatchat.webui_pages.utils:to_json:233 - JSONDecodeError: API未能返回正确的JSON。Expecting value: line 1 column 1 (char 0)
2025-07-31 14:24:28.512 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-31 14:24:28.513 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-31 14:24:34.860 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-31 14:24:34.866 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 14:24:40.351 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-31 14:24:42.786 | ERROR    | chatchat.webui_pages.utils:to_json:233 - JSONDecodeError: API未能返回正确的JSON。Expecting value: line 1 column 1 (char 0)
2025-07-31 14:26:39.249 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-31 14:26:39.252 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-31 14:26:46.777 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-31 14:26:46.784 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 14:26:48.379 | ERROR    | chatchat.webui_pages.utils:post:87 - ConnectError: error when post /knowledge_base/create_knowledge_base: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-31 14:26:50.418 | ERROR    | chatchat.webui_pages.utils:post:87 - ConnectError: error when post /knowledge_base/create_knowledge_base: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-31 14:26:52.461 | ERROR    | chatchat.webui_pages.utils:post:87 - ConnectError: error when post /knowledge_base/create_knowledge_base: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-31 14:26:52.461 | ERROR    | chatchat.webui_pages.utils:to_json:233 - AttributeError: API未能返回正确的JSON。'NoneType' object has no attribute 'json'
2025-07-31 14:26:52.503 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-31 14:26:55.715 | ERROR    | chatchat.webui_pages.utils:to_json:233 - JSONDecodeError: API未能返回正确的JSON。Expecting value: line 1 column 1 (char 0)
2025-07-31 14:28:15.157 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-31 14:28:15.157 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-31 14:28:21.945 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-31 14:28:21.953 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 14:28:27.744 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-31 14:28:31.072 | ERROR    | chatchat.webui_pages.utils:to_json:233 - JSONDecodeError: API未能返回正确的JSON。Expecting value: line 1 column 1 (char 0)
2025-07-31 14:29:22.343 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-31 14:29:22.344 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-31 14:29:29.046 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-31 14:29:29.053 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 14:29:34.548 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-31 14:29:37.010 | ERROR    | chatchat.webui_pages.utils:to_json:233 - JSONDecodeError: API未能返回正确的JSON。Expecting value: line 1 column 1 (char 0)
2025-07-31 14:30:56.467 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-31 14:30:56.467 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-31 14:31:03.523 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-31 14:31:03.529 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 14:31:08.994 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-31 14:31:11.376 | ERROR    | chatchat.webui_pages.utils:to_json:233 - JSONDecodeError: API未能返回正确的JSON。Expecting value: line 1 column 1 (char 0)
2025-07-31 14:31:37.034 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-31 14:31:37.034 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-31 14:39:02.125 | INFO     | __main__:start_main_server:259 - 正在启动服务：
2025-07-31 14:39:02.132 | INFO     | __main__:start_main_server:260 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 14:40:17.097 | ERROR    | chatchat.webui_pages.utils:post:87 - ConnectError: error when post /chat/history: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-31 14:40:17.184 | ERROR    | chatchat.webui_pages.utils:post:87 - ConnectError: error when post /chat/history: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-31 14:40:19.141 | ERROR    | chatchat.webui_pages.utils:post:87 - ConnectError: error when post /chat/history: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-31 14:40:19.226 | ERROR    | chatchat.webui_pages.utils:post:87 - ConnectError: error when post /chat/history: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-31 14:40:20.664 | WARNING  | __main__:start_main_server:317 - Sending SIGKILL to %s
2025-07-31 14:40:20.664 | INFO     | __main__:start_main_server:328 - Process status: %s
2025-07-31 14:40:21.161 | ERROR    | chatchat.webui_pages.utils:post:87 - ConnectError: error when post /chat/history: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-31 14:40:21.161 | ERROR    | chatchat.webui_pages.utils:to_json:233 - AttributeError: API未能返回正确的JSON。'NoneType' object has no attribute 'json'
2025-07-31 14:40:21.265 | ERROR    | chatchat.webui_pages.utils:post:87 - ConnectError: error when post /chat/history: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-31 14:40:21.265 | ERROR    | chatchat.webui_pages.utils:to_json:233 - AttributeError: API未能返回正确的JSON。'NoneType' object has no attribute 'json'
2025-07-31 14:40:29.392 | INFO     | __main__:start_main_server:259 - 正在启动服务：
2025-07-31 14:40:29.397 | INFO     | __main__:start_main_server:260 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 14:42:00.411 | WARNING  | __main__:start_main_server:317 - Sending SIGKILL to %s
2025-07-31 14:42:00.411 | INFO     | __main__:start_main_server:328 - Process status: %s
2025-07-31 14:42:11.920 | INFO     | __main__:start_main_server:259 - 正在启动服务：
2025-07-31 14:42:11.928 | INFO     | __main__:start_main_server:260 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 14:44:19.549 | WARNING  | __main__:start_main_server:317 - Sending SIGKILL to %s
2025-07-31 14:44:19.549 | INFO     | __main__:start_main_server:328 - Process status: %s
2025-07-31 14:44:28.294 | INFO     | __main__:start_main_server:259 - 正在启动服务：
2025-07-31 14:44:28.302 | INFO     | __main__:start_main_server:260 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 14:47:22.056 | WARNING  | __main__:start_main_server:317 - Sending SIGKILL to %s
2025-07-31 14:47:22.056 | INFO     | __main__:start_main_server:328 - Process status: %s
2025-07-31 14:47:31.101 | INFO     | __main__:start_main_server:259 - 正在启动服务：
2025-07-31 14:47:31.108 | INFO     | __main__:start_main_server:260 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 14:58:29.187 | WARNING  | __main__:start_main_server:317 - Sending SIGKILL to %s
2025-07-31 14:58:29.188 | INFO     | __main__:start_main_server:328 - Process status: %s
2025-07-31 14:58:29.190 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-31 14:58:29.191 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-31 15:02:15.599 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-31 15:02:15.608 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 15:02:20.153 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-31 15:02:20.159 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 15:02:21.700 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-31 15:02:23.401 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-31 15:02:38.077 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '2/vector_store/doubao-embedding-text-240715' from disk.
2025-07-31 15:02:49.672 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '1/vector_store/doubao-embedding-text-240715' from disk.
2025-07-31 15:02:50.008 | INFO     | chatchat.server.knowledge_base.utils:file2docs:375 - UnstructuredFileLoader used for E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\knowledge_base\1\content\抖店_夕玲珠宝批发百货_产品.txt
2025-07-31 15:02:56.351 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:save:40 - 已将向量库 ('1', 'doubao-embedding-text-240715') 保存到磁盘
2025-07-31 15:03:15.791 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-31 15:03:15.794 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-31 15:03:16.312 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-31 15:03:16.314 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-31 15:31:30.308 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-31 15:31:30.312 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 15:31:33.725 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-31 15:31:33.737 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 15:31:37.191 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-31 15:31:37.813 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-31 15:34:11.821 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-31 15:34:11.821 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-31 15:34:16.653 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-31 15:34:16.654 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-31 15:34:20.392 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-31 15:34:20.399 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 15:34:24.841 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-31 15:34:24.848 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 15:34:26.980 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-31 15:34:28.620 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-31 15:34:43.903 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '抖店_夕玲珠宝批发百货/vector_store/doubao-embedding-text-240715' from disk.
2025-07-31 15:34:44.186 | INFO     | chatchat.server.knowledge_base.utils:file2docs:375 - UnstructuredFileLoader used for E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\knowledge_base\抖店_夕玲珠宝批发百货\content\抖店_夕玲珠宝批发百货_产品.txt
2025-07-31 15:34:49.824 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:save:40 - 已将向量库 ('抖店_夕玲珠宝批发百货', 'doubao-embedding-text-240715') 保存到磁盘
2025-07-31 17:05:52.936 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-31 17:05:52.945 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 17:05:58.709 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-31 17:06:00.095 | ERROR    | __main__:start_main_server:310 - 'API Server (20756)'
2025-07-31 17:06:00.096 | WARNING  | __main__:start_main_server:311 - Caught KeyboardInterrupt! Setting stop event...
2025-07-31 17:06:00.096 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-31 17:06:00.096 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-31 17:06:25.638 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-31 17:06:25.647 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 17:06:31.260 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-31 17:06:58.787 | ERROR    | chatchat.webui_pages.utils:get:64 - ConnectError: error when get /knowledge_base/list_knowledge_bases: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-31 17:06:58.936 | ERROR    | chatchat.webui_pages.utils:get:64 - ConnectError: error when get /knowledge_base/list_knowledge_bases: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-31 17:07:00.910 | ERROR    | chatchat.webui_pages.utils:get:64 - ConnectError: error when get /knowledge_base/list_knowledge_bases: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-31 17:07:00.991 | ERROR    | chatchat.webui_pages.utils:get:64 - ConnectError: error when get /knowledge_base/list_knowledge_bases: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-31 17:07:02.943 | ERROR    | chatchat.webui_pages.utils:get:64 - ConnectError: error when get /knowledge_base/list_knowledge_bases: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-31 17:07:02.943 | ERROR    | chatchat.webui_pages.utils:to_json:233 - AttributeError: API未能返回正确的JSON。'NoneType' object has no attribute 'json'
2025-07-31 17:07:03.048 | ERROR    | chatchat.webui_pages.utils:get:64 - ConnectError: error when get /knowledge_base/list_knowledge_bases: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-31 17:07:03.048 | ERROR    | chatchat.webui_pages.utils:to_json:233 - AttributeError: API未能返回正确的JSON。'NoneType' object has no attribute 'json'
2025-07-31 17:08:27.526 | ERROR    | chatchat.webui_pages.utils:get:64 - ConnectError: error when get /knowledge_base/list_knowledge_bases: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-31 17:08:27.797 | ERROR    | chatchat.webui_pages.utils:get:64 - ConnectError: error when get /knowledge_base/list_knowledge_bases: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-31 17:08:29.561 | ERROR    | chatchat.webui_pages.utils:get:64 - ConnectError: error when get /knowledge_base/list_knowledge_bases: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-31 17:08:29.840 | ERROR    | chatchat.webui_pages.utils:get:64 - ConnectError: error when get /knowledge_base/list_knowledge_bases: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-31 17:08:31.587 | ERROR    | chatchat.webui_pages.utils:get:64 - ConnectError: error when get /knowledge_base/list_knowledge_bases: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-31 17:08:31.587 | ERROR    | chatchat.webui_pages.utils:to_json:233 - AttributeError: API未能返回正确的JSON。'NoneType' object has no attribute 'json'
2025-07-31 17:08:31.870 | ERROR    | chatchat.webui_pages.utils:get:64 - ConnectError: error when get /knowledge_base/list_knowledge_bases: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-31 17:08:31.870 | ERROR    | chatchat.webui_pages.utils:to_json:233 - AttributeError: API未能返回正确的JSON。'NoneType' object has no attribute 'json'
2025-07-31 17:08:37.037 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-31 17:08:37.042 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 17:08:42.259 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-31 17:29:53.594 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '抖店_夕玲珠宝批发百货/vector_store/doubao-embedding-text-240715' from disk.
2025-07-31 17:34:42.295 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-31 17:34:42.295 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-31 17:34:49.022 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-31 17:34:49.033 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 17:34:54.739 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-31 17:35:13.409 | ERROR    | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:285 - 错误文件: E:\src\Langchain-Chatchat-master\libs\chatchat-server\chatchat\server\chat\kb_chat.py, 行号: 185
错误类型: UnboundLocalError, 错误信息: local variable 'docs' referenced before assignment
2025-07-31 17:35:40.738 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-31 17:35:40.738 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-31 17:35:47.842 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-31 17:35:47.852 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 17:35:53.499 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-31 17:35:57.915 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '抖店_夕玲珠宝批发百货/vector_store/doubao-embedding-text-240715' from disk.
2025-07-31 17:41:43.125 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-31 17:41:43.127 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-31 17:41:49.971 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-31 17:41:49.977 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 17:41:55.830 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-31 17:51:30.315 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '抖店_夕玲珠宝批发百货/vector_store/doubao-embedding-text-240715' from disk.
2025-07-31 18:11:26.688 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-31 18:11:26.690 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-31 18:11:33.571 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-31 18:11:33.578 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 18:11:39.136 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-31 18:13:17.405 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '抖店_夕玲珠宝批发百货/vector_store/doubao-embedding-text-240715' from disk.
2025-07-31 18:23:53.653 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-31 18:23:53.653 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-31 18:24:00.659 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-31 18:24:00.665 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 18:24:06.203 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-31 18:25:00.542 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '抖店_夕玲珠宝批发百货/vector_store/doubao-embedding-text-240715' from disk.
2025-07-31 18:31:26.242 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-31 18:31:26.242 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-31 18:35:25.092 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-31 18:35:25.108 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 18:35:31.004 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-31 18:37:05.353 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-31 18:37:05.353 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-31 18:37:12.259 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-31 18:37:12.274 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 18:37:17.537 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-31 18:38:37.265 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-31 18:38:37.265 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-31 18:38:44.029 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-31 18:38:44.036 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 18:38:49.491 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-31 18:39:12.794 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-31 18:39:12.794 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-31 18:39:19.125 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-31 18:39:19.142 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 18:39:24.369 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-31 18:41:13.810 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-31 18:41:13.810 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-31 18:41:20.519 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-31 18:41:20.534 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 18:41:25.864 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-31 18:42:38.882 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-31 18:42:38.882 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-31 18:42:45.664 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-31 18:42:45.664 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 18:42:51.240 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-31 18:43:00.406 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-31 18:43:00.406 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-31 18:50:46.559 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '抖店_夕玲珠宝批发百货/vector_store/doubao-embedding-text-240715' from disk.
2025-07-31 18:51:44.988 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '抖店_夕玲珠宝批发百货/vector_store/doubao-embedding-text-240715' from disk.
2025-07-31 18:51:50.502 | WARNING  | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:262 - streaming progress has been interrupted by user.
2025-07-31 19:07:31.195 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '抖店_夕玲珠宝批发百货/vector_store/doubao-embedding-text-240715' from disk.
2025-07-31 19:07:54.245 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '抖店_夕玲珠宝批发百货/vector_store/doubao-embedding-text-240715' from disk.
2025-07-31 19:07:54.261 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '抖店_夕玲珠宝批发百货/vector_store/doubao-embedding-text-240715' from disk.
2025-07-31 19:07:54.336 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '抖店_夕玲珠宝批发百货/vector_store/doubao-embedding-text-240715' from disk.
2025-07-31 19:07:54.336 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '抖店_夕玲珠宝批发百货/vector_store/doubao-embedding-text-240715' from disk.
2025-07-31 19:07:54.639 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '抖店_夕玲珠宝批发百货/vector_store/doubao-embedding-text-240715' from disk.
2025-07-31 19:07:54.731 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '抖店_夕玲珠宝批发百货/vector_store/doubao-embedding-text-240715' from disk.
2025-07-31 19:21:23.211 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-07-31 19:21:23.221 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 19:21:29.059 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-31 19:22:09.318 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-07-31 19:22:09.318 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-07-31 19:22:15.637 | INFO     | __main__:start_main_server:255 - 正在启动服务：
2025-07-31 19:22:15.648 | INFO     | __main__:start_main_server:256 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 19:22:20.902 | INFO     | __mp_main__:run_api_server:53 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-31 19:22:28.985 | ERROR    | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:274 - 错误文件: E:\src\Langchain-Chatchat-master\libs\chatchat-server\chatchat\server\chat\kb_chat.py, 行号: 181
错误类型: UnboundLocalError, 错误信息: local variable 'docs' referenced before assignment
2025-07-31 19:23:48.690 | WARNING  | __main__:start_main_server:313 - Sending SIGKILL to %s
2025-07-31 19:23:48.690 | INFO     | __main__:start_main_server:324 - Process status: %s
2025-07-31 19:23:55.296 | INFO     | __main__:start_main_server:255 - 正在启动服务：
2025-07-31 19:23:55.306 | INFO     | __main__:start_main_server:256 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 19:24:00.705 | INFO     | __mp_main__:run_api_server:53 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-31 19:32:16.870 | WARNING  | __main__:start_main_server:313 - Sending SIGKILL to %s
2025-07-31 19:32:16.871 | INFO     | __main__:start_main_server:324 - Process status: %s
2025-07-31 19:32:24.199 | INFO     | __main__:start_main_server:255 - 正在启动服务：
2025-07-31 19:32:24.204 | INFO     | __main__:start_main_server:256 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 19:32:30.498 | INFO     | __mp_main__:run_api_server:53 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-31 19:34:39.434 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '抖店_夕玲珠宝批发百货/vector_store/doubao-embedding-text-240715' from disk.
2025-07-31 20:45:12.922 | INFO     | __main__:start_main_server:255 - 正在启动服务：
2025-07-31 20:45:12.924 | INFO     | __main__:start_main_server:256 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 20:45:14.502 | INFO     | __mp_main__:run_webui:78 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-31 20:46:54.182 | WARNING  | __main__:start_main_server:313 - Sending SIGKILL to %s
2025-07-31 20:46:54.184 | INFO     | __main__:start_main_server:324 - Process status: %s
2025-07-31 20:46:58.406 | INFO     | __main__:start_main_server:255 - 正在启动服务：
2025-07-31 20:46:58.408 | INFO     | __main__:start_main_server:256 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 20:46:59.983 | INFO     | __mp_main__:run_webui:78 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-31 21:03:48.742 | WARNING  | __main__:start_main_server:313 - Sending SIGKILL to %s
2025-07-31 21:03:48.743 | INFO     | __main__:start_main_server:324 - Process status: %s
2025-07-31 21:03:51.968 | INFO     | __main__:start_main_server:255 - 正在启动服务：
2025-07-31 21:03:51.970 | INFO     | __main__:start_main_server:256 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 21:03:53.606 | INFO     | __mp_main__:run_webui:78 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-31 21:05:16.742 | WARNING  | __main__:start_main_server:313 - Sending SIGKILL to %s
2025-07-31 21:05:16.743 | INFO     | __main__:start_main_server:324 - Process status: %s
2025-07-31 21:05:19.688 | INFO     | __main__:start_main_server:255 - 正在启动服务：
2025-07-31 21:05:19.690 | INFO     | __main__:start_main_server:256 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 21:05:21.251 | INFO     | __mp_main__:run_webui:78 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-31 21:21:45.003 | INFO     | __main__:start_main_server:255 - 正在启动服务：
2025-07-31 21:21:45.011 | INFO     | __main__:start_main_server:256 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 21:21:52.041 | INFO     | __mp_main__:run_api_server:53 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-31 21:21:53.234 | ERROR    | __main__:start_main_server:309 - 'API Server (2672)'
2025-07-31 21:21:53.235 | WARNING  | __main__:start_main_server:310 - Caught KeyboardInterrupt! Setting stop event...
2025-07-31 21:21:53.235 | WARNING  | __main__:start_main_server:313 - Sending SIGKILL to %s
2025-07-31 21:21:53.235 | INFO     | __main__:start_main_server:324 - Process status: %s
2025-07-31 21:22:20.137 | ERROR    | chatchat.webui_pages.utils:get:64 - ConnectError: error when get /knowledge_base/list_knowledge_bases: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-31 21:22:22.170 | ERROR    | chatchat.webui_pages.utils:get:64 - ConnectError: error when get /knowledge_base/list_knowledge_bases: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-31 21:22:24.209 | ERROR    | chatchat.webui_pages.utils:get:64 - ConnectError: error when get /knowledge_base/list_knowledge_bases: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-07-31 21:22:24.210 | ERROR    | chatchat.webui_pages.utils:to_json:233 - AttributeError: API未能返回正确的JSON。'NoneType' object has no attribute 'json'
2025-07-31 21:22:27.561 | INFO     | __main__:start_main_server:255 - 正在启动服务：
2025-07-31 21:22:27.569 | INFO     | __main__:start_main_server:256 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 21:22:34.083 | INFO     | __mp_main__:run_api_server:53 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-31 21:28:00.857 | INFO     | __main__:start_main_server:255 - 正在启动服务：
2025-07-31 21:28:00.859 | INFO     | __main__:start_main_server:256 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-07-31 21:28:02.633 | INFO     | __mp_main__:run_webui:78 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-07-31 21:28:11.039 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '抖店_夕玲珠宝批发百货/vector_store/doubao-embedding-text-240715' from disk.
2025-07-31 21:28:20.373 | WARNING  | __main__:start_main_server:313 - Sending SIGKILL to %s
2025-07-31 21:28:20.375 | INFO     | __main__:start_main_server:324 - Process status: %s
2025-08-01 10:02:48.205 | INFO     | __main__:start_main_server:255 - 正在启动服务：
2025-08-01 10:02:48.217 | INFO     | __main__:start_main_server:256 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 10:02:53.934 | INFO     | __mp_main__:run_api_server:53 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 10:30:19.785 | INFO     | __main__:start_main_server:255 - 正在启动服务：
2025-08-01 10:30:19.787 | INFO     | __main__:start_main_server:256 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 10:30:21.356 | INFO     | __mp_main__:run_webui:78 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 10:32:56.977 | WARNING  | __main__:start_main_server:313 - Sending SIGKILL to %s
2025-08-01 10:32:56.977 | INFO     | __main__:start_main_server:324 - Process status: %s
2025-08-01 10:32:59.969 | INFO     | __main__:start_main_server:255 - 正在启动服务：
2025-08-01 10:32:59.971 | INFO     | __main__:start_main_server:256 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 10:33:01.490 | INFO     | __mp_main__:run_webui:78 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 11:00:07.754 | WARNING  | __main__:start_main_server:313 - Sending SIGKILL to %s
2025-08-01 11:00:07.755 | INFO     | __main__:start_main_server:324 - Process status: %s
2025-08-01 11:38:55.050 | WARNING  | __main__:start_main_server:313 - Sending SIGKILL to %s
2025-08-01 11:38:55.053 | INFO     | __main__:start_main_server:324 - Process status: %s
2025-08-01 11:39:01.923 | INFO     | __main__:start_main_server:255 - 正在启动服务：
2025-08-01 11:39:01.930 | INFO     | __main__:start_main_server:256 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 11:40:20.989 | WARNING  | __main__:start_main_server:313 - Sending SIGKILL to %s
2025-08-01 11:40:20.990 | INFO     | __main__:start_main_server:324 - Process status: %s
2025-08-01 11:40:27.188 | INFO     | __main__:start_main_server:255 - 正在启动服务：
2025-08-01 11:40:27.195 | INFO     | __main__:start_main_server:256 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 11:41:13.618 | INFO     | __mp_main__:run_api_server:53 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 11:42:18.952 | INFO     | __main__:start_main_server:255 - 正在启动服务：
2025-08-01 11:42:18.955 | INFO     | __main__:start_main_server:256 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 11:42:20.512 | INFO     | __mp_main__:run_webui:78 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 11:47:22.423 | ERROR    | chatchat.webui_pages.utils:get:64 - ReadTimeout: error when get /knowledge_base/list_knowledge_bases: timed out
2025-08-01 12:02:50.594 | WARNING  | __main__:start_main_server:313 - Sending SIGKILL to %s
2025-08-01 12:02:50.594 | INFO     | __main__:start_main_server:324 - Process status: %s
2025-08-01 12:02:51.154 | WARNING  | __main__:start_main_server:313 - Sending SIGKILL to %s
2025-08-01 12:02:51.154 | INFO     | __main__:start_main_server:324 - Process status: %s
2025-08-01 12:05:22.699 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 12:05:22.701 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 12:05:25.674 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 12:05:40.851 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 12:05:40.853 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 12:05:42.410 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 12:07:21.648 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 12:07:21.649 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 12:07:24.892 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 12:07:24.894 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 12:07:26.495 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 12:07:35.934 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '抖店_夕玲珠宝批发百货/vector_store/doubao-embedding-text-240715' from disk.
2025-08-01 12:07:50.432 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 12:07:50.432 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 12:07:53.397 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 12:07:53.397 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 12:10:34.510 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 12:10:34.512 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 12:10:37.432 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 12:10:59.000 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '抖店_夕玲珠宝批发百货/vector_store/doubao-embedding-text-240715' from disk.
2025-08-01 13:07:09.122 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 13:07:09.126 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 14:12:36.114 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 14:12:36.115 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 14:12:37.856 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 14:12:42.299 | ERROR    | chatchat.webui_pages.utils:get:64 - ConnectError: error when get /knowledge_base/list_knowledge_bases: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-08-01 14:12:43.492 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 14:12:43.500 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 14:12:44.341 | ERROR    | chatchat.webui_pages.utils:get:64 - ConnectError: error when get /knowledge_base/list_knowledge_bases: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-08-01 14:12:46.390 | ERROR    | chatchat.webui_pages.utils:get:64 - ConnectError: error when get /knowledge_base/list_knowledge_bases: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-08-01 14:12:46.390 | ERROR    | chatchat.webui_pages.utils:to_json:233 - AttributeError: API未能返回正确的JSON。'NoneType' object has no attribute 'json'
2025-08-01 14:12:49.226 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 14:21:11.493 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 14:21:11.494 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 14:21:15.301 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 14:21:15.301 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 14:21:18.961 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 14:21:18.963 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 14:21:19.265 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 14:21:19.272 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 14:21:20.634 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 14:21:25.764 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 14:21:25.821 | ERROR    | chatchat.webui_pages.utils:get:64 - ConnectError: error when get /knowledge_base/list_knowledge_bases: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-08-01 14:21:57.422 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 14:21:57.423 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 14:22:00.577 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 14:22:00.579 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 14:22:02.090 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 14:22:35.711 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 14:22:35.713 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 14:22:41.862 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 14:22:41.869 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 14:22:45.031 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 14:25:03.271 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '0ab3e66e96f94e11be53e59c70c2aa62/vector_store/doubao-embedding-text-240715' from disk.
2025-08-01 14:26:29.808 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 14:26:29.809 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 14:26:36.803 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 14:26:36.808 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 14:26:39.883 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 14:27:12.888 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 14:27:12.888 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 14:27:19.996 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 14:27:20.003 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 14:27:23.188 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 14:28:27.168 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 14:28:27.168 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 14:28:33.970 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 14:28:33.975 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 14:28:37.065 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 14:29:47.520 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 14:29:47.521 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 14:29:54.132 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 14:29:54.139 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 14:29:57.228 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 14:29:59.637 | ERROR    | chatchat.webui_pages.utils:to_json:233 - JSONDecodeError: API未能返回正确的JSON。Expecting value: line 1 column 1 (char 0)
2025-08-01 14:29:59.647 | ERROR    | chatchat.webui_pages.utils:to_json:233 - JSONDecodeError: API未能返回正确的JSON。Expecting value: line 1 column 1 (char 0)
2025-08-01 14:31:10.325 | ERROR    | chatchat.webui_pages.utils:to_json:233 - JSONDecodeError: API未能返回正确的JSON。Expecting value: line 1 column 1 (char 0)
2025-08-01 14:32:13.140 | ERROR    | chatchat.webui_pages.utils:to_json:233 - JSONDecodeError: API未能返回正确的JSON。Expecting value: line 1 column 1 (char 0)
2025-08-01 14:33:20.446 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 14:33:20.448 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 14:33:24.198 | ERROR    | chatchat.webui_pages.utils:to_json:233 - JSONDecodeError: API未能返回正确的JSON。Expecting value: line 1 column 1 (char 0)
2025-08-01 14:33:27.326 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 14:33:27.333 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 14:33:32.572 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 14:37:38.976 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 14:37:38.976 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 14:37:46.571 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 14:37:46.579 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 14:37:49.780 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 14:44:24.617 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 14:44:24.617 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 14:44:31.348 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 14:44:31.353 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 14:44:34.388 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 14:46:05.792 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 14:46:05.793 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 14:46:12.778 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 14:46:12.786 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 14:46:15.988 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 14:46:28.794 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 14:46:28.795 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 14:46:35.511 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 14:46:35.519 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 14:46:38.580 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 15:18:55.152 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 15:18:55.152 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 15:19:01.897 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 15:19:01.907 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 15:19:05.139 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 15:20:57.790 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 15:20:57.790 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 15:21:04.893 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 15:21:04.900 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 15:21:08.104 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 15:22:29.439 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 15:22:29.440 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 15:22:36.435 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 15:22:36.441 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 15:22:39.459 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 15:35:49.761 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 15:35:49.761 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 15:35:56.343 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 15:35:56.353 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 15:35:59.601 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 15:42:41.661 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 15:42:41.661 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 15:42:48.170 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 15:42:48.176 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 15:42:51.230 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 15:43:53.677 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 15:43:53.678 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 15:44:00.807 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 15:44:00.814 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 15:44:03.906 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 15:47:31.381 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 15:47:31.382 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 15:47:38.283 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 15:47:38.289 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 15:47:41.418 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 15:49:29.773 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 15:49:29.774 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 15:49:36.241 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 15:49:36.248 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 15:49:39.925 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 15:51:03.639 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 15:51:03.640 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 15:51:10.472 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 15:51:10.483 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 15:51:14.001 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 15:54:39.823 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 15:54:39.824 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 15:54:47.131 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 15:54:47.138 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 15:54:50.316 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 15:55:19.158 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 15:55:19.158 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 15:55:26.098 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 15:55:26.108 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 15:55:29.132 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 15:56:56.575 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '638f3650df0e4e1db1a548d4013744eb/vector_store/doubao-embedding-text-240715' from disk.
2025-08-01 15:56:56.969 | ERROR    | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:273 - 错误文件: E:\src\Langchain-Chatchat-master\.venv\lib\site-packages\langchain_community\retrievers\bm25.py, 行号: 90
错误类型: ValueError, 错误信息: not enough values to unpack (expected 2, got 0)
2025-08-01 15:58:23.146 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '453ed6e79a3e4b44941f52421916b192/vector_store/doubao-embedding-text-240715' from disk.
2025-08-01 15:58:23.476 | ERROR    | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:273 - 错误文件: E:\src\Langchain-Chatchat-master\.venv\lib\site-packages\langchain_community\retrievers\bm25.py, 行号: 90
错误类型: ValueError, 错误信息: not enough values to unpack (expected 2, got 0)
2025-08-01 15:59:17.213 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '265b579d19ab470fbfd9a2bfbd659905/vector_store/doubao-embedding-text-240715' from disk.
2025-08-01 15:59:19.996 | ERROR    | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:273 - 错误文件: E:\src\Langchain-Chatchat-master\.venv\lib\site-packages\langchain_community\retrievers\bm25.py, 行号: 90
错误类型: ValueError, 错误信息: not enough values to unpack (expected 2, got 0)
2025-08-01 16:00:58.891 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '3600d8cd6f1a402a8e3076a27c54982c/vector_store/doubao-embedding-text-240715' from disk.
2025-08-01 16:01:01.372 | ERROR    | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:273 - 错误文件: E:\src\Langchain-Chatchat-master\.venv\lib\site-packages\langchain_community\retrievers\bm25.py, 行号: 90
错误类型: ValueError, 错误信息: not enough values to unpack (expected 2, got 0)
2025-08-01 16:02:45.642 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 16:02:45.648 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 16:02:52.618 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 16:02:52.625 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 16:02:58.633 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 16:11:52.530 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 16:11:52.530 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 16:12:44.561 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 16:12:44.563 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 16:12:46.075 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 16:12:46.082 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 16:12:46.402 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 16:12:52.417 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 16:13:42.919 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 16:13:42.920 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 16:17:10.439 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 16:17:10.446 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 16:17:15.883 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 16:17:45.267 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '抖店_夕玲珠宝批发百货/vector_store/doubao-embedding-text-240715' from disk.
2025-08-01 16:23:38.118 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 16:23:38.119 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 16:23:41.441 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 16:23:41.443 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 16:23:42.916 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 16:24:25.269 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 16:24:25.270 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 16:24:31.563 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 16:24:31.570 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 16:24:34.722 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 16:24:59.801 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 16:24:59.802 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 16:25:06.600 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 16:25:06.607 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 16:25:09.820 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 16:25:19.871 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 16:25:19.872 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 16:25:27.102 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 16:25:27.109 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 16:25:30.337 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 16:28:46.639 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 16:28:46.639 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 16:28:53.611 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 16:28:53.618 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 16:28:56.855 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 16:30:11.230 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 16:30:11.230 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 16:30:18.114 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 16:30:18.119 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 16:30:18.195 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 16:30:18.196 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 16:30:24.359 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 16:30:25.422 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 16:30:25.427 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 16:30:28.464 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 16:30:46.333 | INFO     | chatchat.server.knowledge_base.utils:file2docs:375 - UnstructuredFileLoader used for E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\knowledge_base\0ab3e66e96f94e11be53e59c70c2aa62\content\抖店_夕玲珠宝批发百货_产品.txt
2025-08-01 16:30:51.591 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '0ab3e66e96f94e11be53e59c70c2aa62/vector_store/doubao-embedding-text-240715' from disk.
2025-08-01 16:30:53.423 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:save:40 - 已将向量库 ('0ab3e66e96f94e11be53e59c70c2aa62', 'doubao-embedding-text-240715') 保存到磁盘
2025-08-01 16:31:50.807 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:save:40 - 已将向量库 ('0ab3e66e96f94e11be53e59c70c2aa62', 'doubao-embedding-text-240715') 保存到磁盘
2025-08-01 16:32:31.104 | INFO     | chatchat.server.knowledge_base.utils:file2docs:375 - UnstructuredFileLoader used for E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\knowledge_base\2\content\history_id_123_20250801.txt
2025-08-01 16:32:31.466 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '2/vector_store/doubao-embedding-text-240715' from disk.
2025-08-01 16:32:32.169 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:save:40 - 已将向量库 ('2', 'doubao-embedding-text-240715') 保存到磁盘
2025-08-01 16:32:50.055 | INFO     | chatchat.server.knowledge_base.utils:file2docs:375 - UnstructuredFileLoader used for E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\knowledge_base\0ab3e66e96f94e11be53e59c70c2aa62\content\history_id_118_20250801.txt
2025-08-01 16:32:50.891 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:save:40 - 已将向量库 ('0ab3e66e96f94e11be53e59c70c2aa62', 'doubao-embedding-text-240715') 保存到磁盘
2025-08-01 17:02:07.770 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 17:02:07.770 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 17:02:10.885 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 17:02:10.885 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 17:02:16.060 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 17:02:16.071 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 17:02:18.775 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 17:02:18.791 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 17:02:21.873 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 17:02:22.080 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 17:02:59.503 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '2aca092e8cc94165b489898f45c16ac8/vector_store/doubao-embedding-text-240715' from disk.
2025-08-01 17:11:12.511 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 17:11:12.511 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 17:11:12.519 | ERROR    | chatchat.webui_pages.utils:post:87 - ReadError: error when post /knowledge_base/create_knowledge_base: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
2025-08-01 17:11:14.576 | ERROR    | chatchat.webui_pages.utils:post:87 - ConnectError: error when post /knowledge_base/create_knowledge_base: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-08-01 17:11:16.617 | ERROR    | chatchat.webui_pages.utils:post:87 - ConnectError: error when post /knowledge_base/create_knowledge_base: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-08-01 17:11:16.618 | ERROR    | chatchat.webui_pages.utils:to_json:233 - AttributeError: API未能返回正确的JSON。'NoneType' object has no attribute 'json'
2025-08-01 17:11:27.387 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 17:11:27.395 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 17:11:32.840 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 17:16:28.283 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 17:16:28.285 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 17:16:34.864 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 17:16:34.869 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 17:16:40.331 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 17:17:16.826 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '4e8f983fe1db45a79f506a9d717a1ee2/vector_store/doubao-embedding-text-240715' from disk.
2025-08-01 17:18:05.223 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in 'db1d7ca20a3b4e33830c19383db65caf/vector_store/doubao-embedding-text-240715' from disk.
2025-08-01 17:20:19.511 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 17:20:19.511 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 17:20:25.945 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 17:20:25.951 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 17:20:31.875 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 17:20:34.467 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '2227ffdac6574755bd067980bf9df0c1/vector_store/doubao-embedding-text-240715' from disk.
2025-08-01 17:20:59.086 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '03f580b466454fc291445c356a5873d3/vector_store/doubao-embedding-text-240715' from disk.
2025-08-01 17:21:14.356 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 17:21:14.356 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 17:21:20.867 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 17:21:20.872 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 17:21:26.702 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 17:22:33.311 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 17:22:33.311 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 17:22:33.315 | ERROR    | chatchat.webui_pages.utils:post:87 - ReadError: error when post /knowledge_base/create_knowledge_base: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
2025-08-01 17:22:35.355 | ERROR    | chatchat.webui_pages.utils:post:87 - ConnectError: error when post /knowledge_base/create_knowledge_base: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-08-01 17:22:37.390 | ERROR    | chatchat.webui_pages.utils:post:87 - ConnectError: error when post /knowledge_base/create_knowledge_base: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-08-01 17:22:37.390 | ERROR    | chatchat.webui_pages.utils:to_json:233 - AttributeError: API未能返回正确的JSON。'NoneType' object has no attribute 'json'
2025-08-01 17:22:39.636 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 17:22:39.646 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 17:22:42.517 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 17:22:42.518 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 17:22:48.747 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 17:22:48.754 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 17:22:54.277 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 17:23:14.698 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 17:23:14.698 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 17:23:14.703 | ERROR    | chatchat.webui_pages.utils:post:87 - ReadError: error when post /knowledge_base/create_knowledge_base: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
2025-08-01 17:23:16.746 | ERROR    | chatchat.webui_pages.utils:post:87 - ConnectError: error when post /knowledge_base/create_knowledge_base: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-08-01 17:23:18.771 | ERROR    | chatchat.webui_pages.utils:post:87 - ConnectError: error when post /knowledge_base/create_knowledge_base: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-08-01 17:23:18.772 | ERROR    | chatchat.webui_pages.utils:to_json:233 - AttributeError: API未能返回正确的JSON。'NoneType' object has no attribute 'json'
2025-08-01 17:23:21.146 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 17:23:21.152 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 17:23:26.582 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 17:23:35.150 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in 'ebe956ad5b4d4dcb8d104089825fafaa/vector_store/doubao-embedding-text-240715' from disk.
2025-08-01 17:28:03.644 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '抖店_夕玲珠宝批发百货/vector_store/doubao-embedding-text-240715' from disk.
2025-08-01 17:28:36.141 | INFO     | chatchat.server.knowledge_base.utils:file2docs:375 - UnstructuredFileLoader used for E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\knowledge_base\4e8f983fe1db45a79f506a9d717a1ee2\content\history_id_122_20250801.txt
2025-08-01 17:28:39.530 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '4e8f983fe1db45a79f506a9d717a1ee2/vector_store/doubao-embedding-text-240715' from disk.
2025-08-01 17:28:40.041 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:save:40 - 已将向量库 ('4e8f983fe1db45a79f506a9d717a1ee2', 'doubao-embedding-text-240715') 保存到磁盘
2025-08-01 17:28:48.550 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '0ab3e66e96f94e11be53e59c70c2aa62/vector_store/doubao-embedding-text-240715' from disk.
2025-08-01 17:29:02.477 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:save:40 - 已将向量库 ('4e8f983fe1db45a79f506a9d717a1ee2', 'doubao-embedding-text-240715') 保存到磁盘
2025-08-01 17:29:43.535 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:save:40 - 已将向量库 ('0ab3e66e96f94e11be53e59c70c2aa62', 'doubao-embedding-text-240715') 保存到磁盘
2025-08-01 17:29:54.218 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 17:29:54.218 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 17:30:00.528 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 17:30:00.533 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 17:30:06.038 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 17:31:06.631 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 17:31:06.631 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 17:31:13.216 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 17:31:13.227 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 17:31:18.794 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 17:33:04.092 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in 'aff8bc150d2f4b598d5829d78023b01e/vector_store/doubao-embedding-text-240715' from disk.
2025-08-01 17:34:45.583 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '抖店_夕玲珠宝批发百货/vector_store/doubao-embedding-text-240715' from disk.
2025-08-01 17:35:06.739 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 17:35:06.739 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 17:35:13.255 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 17:35:13.262 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 17:35:18.770 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 17:35:24.838 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 17:35:24.838 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 17:35:31.598 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 17:35:31.605 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 17:35:34.679 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 17:36:01.471 | INFO     | chatchat.server.knowledge_base.utils:file2docs:375 - UnstructuredFileLoader used for E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\knowledge_base\aff8bc150d2f4b598d5829d78023b01e\content\history_id_125_20250801.txt
2025-08-01 17:36:06.293 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in 'aff8bc150d2f4b598d5829d78023b01e/vector_store/doubao-embedding-text-240715' from disk.
2025-08-01 17:36:06.853 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:save:40 - 已将向量库 ('aff8bc150d2f4b598d5829d78023b01e', 'doubao-embedding-text-240715') 保存到磁盘
2025-08-01 17:36:24.694 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in 'aff8bc150d2f4b598d5829d78023b01e/vector_store/doubao-embedding-text-240715' from disk.
2025-08-01 17:36:24.970 | INFO     | chatchat.server.knowledge_base.utils:file2docs:375 - UnstructuredFileLoader used for E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\knowledge_base\aff8bc150d2f4b598d5829d78023b01e\content\history_id_125_20250801.txt
2025-08-01 17:36:25.421 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:save:40 - 已将向量库 ('aff8bc150d2f4b598d5829d78023b01e', 'doubao-embedding-text-240715') 保存到磁盘
2025-08-01 17:36:30.613 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:save:40 - 已将向量库 ('aff8bc150d2f4b598d5829d78023b01e', 'doubao-embedding-text-240715') 保存到磁盘
2025-08-01 17:36:43.765 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '45e069d0986043d1aa6fa6248c755144/vector_store/doubao-embedding-text-240715' from disk.
2025-08-01 17:46:46.721 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 17:46:46.721 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 17:46:53.348 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 17:46:53.355 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 17:46:56.538 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 17:49:08.179 | ERROR    | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:273 - 错误文件: E:\src\Langchain-Chatchat-master\.venv\lib\site-packages\langchain_community\retrievers\bm25.py, 行号: 90
错误类型: ValueError, 错误信息: not enough values to unpack (expected 2, got 0)
2025-08-01 17:50:12.893 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 17:50:12.894 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 17:50:20.002 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 17:50:20.010 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 17:50:23.216 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 17:50:37.739 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '抖店_夕玲珠宝批发百货/vector_store/doubao-embedding-text-240715' from disk.
2025-08-01 17:52:57.097 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 17:52:57.097 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 17:53:03.805 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 17:53:03.812 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 17:53:07.165 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 17:54:07.972 | ERROR    | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:273 - 错误文件: E:\src\Langchain-Chatchat-master\.venv\lib\site-packages\langchain_community\retrievers\bm25.py, 行号: 90
错误类型: ValueError, 错误信息: not enough values to unpack (expected 2, got 0)
2025-08-01 17:55:25.177 | ERROR    | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:273 - 错误文件: E:\src\Langchain-Chatchat-master\.venv\lib\site-packages\langchain_community\retrievers\bm25.py, 行号: 90
错误类型: ValueError, 错误信息: not enough values to unpack (expected 2, got 0)
2025-08-01 18:00:26.898 | INFO     | chatchat.server.knowledge_base.utils:file2docs:375 - UnstructuredFileLoader used for E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\knowledge_base\45e069d0986043d1aa6fa6248c755144\content\history_id_126_20250801.txt
2025-08-01 18:00:27.703 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:save:40 - 已将向量库 ('45e069d0986043d1aa6fa6248c755144', 'doubao-embedding-text-240715') 保存到磁盘
2025-08-01 18:02:59.964 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 18:02:59.965 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 18:03:01.150 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 18:03:01.150 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 18:03:07.466 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 18:03:07.472 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 18:03:09.981 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 18:03:09.986 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 18:03:10.867 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 18:03:16.365 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 18:07:31.548 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '45e069d0986043d1aa6fa6248c755144/vector_store/doubao-embedding-text-240715' from disk.
2025-08-01 18:07:40.800 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '抖店_夕玲珠宝批发百货/vector_store/doubao-embedding-text-240715' from disk.
2025-08-01 18:08:34.665 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in 'ea427942c555405e88df3da1e354602d/vector_store/doubao-embedding-text-240715' from disk.
2025-08-01 18:16:36.666 | INFO     | chatchat.server.knowledge_base.utils:file2docs:375 - UnstructuredFileLoader used for E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\knowledge_base\ea427942c555405e88df3da1e354602d\content\history_id_125_20250801.txt
2025-08-01 18:16:41.145 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:save:40 - 已将向量库 ('ea427942c555405e88df3da1e354602d', 'doubao-embedding-text-240715') 保存到磁盘
2025-08-01 18:16:48.994 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in 'ea427942c555405e88df3da1e354602d/vector_store/doubao-embedding-text-240715' from disk.
2025-08-01 18:16:49.678 | INFO     | chatchat.server.knowledge_base.utils:file2docs:375 - UnstructuredFileLoader used for E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\knowledge_base\ea427942c555405e88df3da1e354602d\content\history_id_125_20250801.txt
2025-08-01 18:16:50.366 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:save:40 - 已将向量库 ('ea427942c555405e88df3da1e354602d', 'doubao-embedding-text-240715') 保存到磁盘
2025-08-01 18:17:00.743 | INFO     | chatchat.server.knowledge_base.utils:file2docs:375 - UnstructuredFileLoader used for E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\knowledge_base\ea427942c555405e88df3da1e354602d\content\history_id_125_20250801.txt
2025-08-01 18:17:01.751 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:save:40 - 已将向量库 ('ea427942c555405e88df3da1e354602d', 'doubao-embedding-text-240715') 保存到磁盘
2025-08-01 18:18:00.824 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '83d31dc5b5564fc780b12ff24d251f68/vector_store/doubao-embedding-text-240715' from disk.
2025-08-01 18:18:15.937 | INFO     | chatchat.server.knowledge_base.utils:file2docs:375 - UnstructuredFileLoader used for E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\knowledge_base\83d31dc5b5564fc780b12ff24d251f68\content\test (1).txt
2025-08-01 18:18:17.806 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:save:40 - 已将向量库 ('83d31dc5b5564fc780b12ff24d251f68', 'doubao-embedding-text-240715') 保存到磁盘
2025-08-01 18:19:32.841 | INFO     | chatchat.server.knowledge_base.utils:file2docs:375 - UnstructuredFileLoader used for E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\knowledge_base\83d31dc5b5564fc780b12ff24d251f68\content\history_id_128_20250801.txt
2025-08-01 18:19:33.584 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:save:40 - 已将向量库 ('83d31dc5b5564fc780b12ff24d251f68', 'doubao-embedding-text-240715') 保存到磁盘
2025-08-01 18:20:00.372 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 18:20:00.372 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 18:20:07.108 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 18:20:07.112 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 18:20:11.329 | ERROR    | chatchat.webui_pages.utils:post:87 - ConnectError: error when post /chat/history: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-08-01 18:20:13.286 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 18:20:13.351 | ERROR    | chatchat.webui_pages.utils:post:87 - ConnectError: error when post /chat/history: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
2025-08-01 18:20:40.126 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in 'ea427942c555405e88df3da1e354602d/vector_store/doubao-embedding-text-240715' from disk.
2025-08-01 18:22:08.753 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '83d31dc5b5564fc780b12ff24d251f68/vector_store/doubao-embedding-text-240715' from disk.
2025-08-01 18:22:09.206 | INFO     | chatchat.server.knowledge_base.utils:file2docs:375 - UnstructuredFileLoader used for E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\knowledge_base\83d31dc5b5564fc780b12ff24d251f68\content\history_id_128_20250801.txt
2025-08-01 18:22:09.211 | INFO     | chatchat.server.knowledge_base.utils:file2docs:375 - UnstructuredFileLoader used for E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\knowledge_base\83d31dc5b5564fc780b12ff24d251f68\content\test (1).txt
2025-08-01 18:22:14.288 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:save:40 - 已将向量库 ('83d31dc5b5564fc780b12ff24d251f68', 'doubao-embedding-text-240715') 保存到磁盘
2025-08-01 18:23:38.783 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '831430a148024403bf8bd2ec4a049069/vector_store/doubao-embedding-text-240715' from disk.
2025-08-01 18:30:57.299 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 18:30:57.300 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 18:31:03.789 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 18:31:03.798 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 18:31:09.293 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 18:31:15.343 | ERROR    | chatchat.webui_pages.utils:to_json:233 - JSONDecodeError: API未能返回正确的JSON。Expecting value: line 1 column 1 (char 0)
2025-08-01 18:31:33.749 | ERROR    | chatchat.webui_pages.utils:to_json:233 - JSONDecodeError: API未能返回正确的JSON。Expecting value: line 1 column 1 (char 0)
2025-08-01 18:32:05.867 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 18:32:05.868 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 18:32:12.699 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 18:32:12.707 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 18:32:18.398 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 18:32:23.072 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '7507a119497949daa796e10e363e519f/vector_store/doubao-embedding-text-240715' from disk.
2025-08-01 18:32:58.879 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 18:32:58.879 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 18:33:06.288 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 18:33:06.294 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 18:33:11.895 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 19:26:49.653 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 19:26:49.653 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 19:26:56.479 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 19:26:56.490 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 19:27:03.112 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 19:27:58.686 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '5ac17866cf694bc4b0d77aab8d8752fa/vector_store/doubao-embedding-text-240715' from disk.
2025-08-01 19:31:25.914 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 19:31:25.914 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 19:31:32.524 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 19:31:32.532 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 19:31:39.333 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 19:32:15.947 | INFO     | chatchat.server.knowledge_base.utils:file2docs:375 - UnstructuredFileLoader used for E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\knowledge_base\5ac17866cf694bc4b0d77aab8d8752fa\content\拼多多_试试看_产品.txt
2025-08-01 19:32:19.825 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '5ac17866cf694bc4b0d77aab8d8752fa/vector_store/doubao-embedding-text-240715' from disk.
2025-08-01 19:32:20.365 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:save:40 - 已将向量库 ('5ac17866cf694bc4b0d77aab8d8752fa', 'doubao-embedding-text-240715') 保存到磁盘
2025-08-01 19:33:11.971 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 19:33:11.971 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 19:33:19.654 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 19:33:19.661 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 19:33:25.024 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 19:33:29.498 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '5ac17866cf694bc4b0d77aab8d8752fa/vector_store/doubao-embedding-text-240715' from disk.
2025-08-01 19:34:01.545 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 19:34:01.546 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 19:34:08.436 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 19:34:08.446 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 19:34:14.432 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 19:34:24.967 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in 'db438bbc8ee743cd84f46fb9f38902f7/vector_store/doubao-embedding-text-240715' from disk.
2025-08-01 19:34:37.696 | INFO     | chatchat.server.knowledge_base.utils:file2docs:375 - UnstructuredFileLoader used for E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\knowledge_base\db438bbc8ee743cd84f46fb9f38902f7\content\shop_products.txt
2025-08-01 19:34:41.827 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:save:40 - 已将向量库 ('db438bbc8ee743cd84f46fb9f38902f7', 'doubao-embedding-text-240715') 保存到磁盘
2025-08-01 19:37:33.685 | WARNING  | chatchat.server.db.db_handler:set_platform_api_key:357 - 平台不存在: ID=3, 类型=ollama
2025-08-01 19:39:44.364 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '9e42cfc5da394824b81e456b97e7fa09/vector_store/doubao-embedding-text-240715' from disk.
2025-08-01 19:49:10.013 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 19:49:10.014 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 19:49:11.032 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 19:49:11.037 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 20:32:04.026 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 20:32:04.042 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\src\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 20:32:09.681 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 20:32:52.659 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '抖店_夕玲珠宝批发百货/vector_store/doubao-embedding-text-240715' from disk.
2025-08-01 20:32:53.675 | ERROR    | chatchat.server.utils:get_ChatOpenAI:282 - failed to create ChatOpenAI for model: qwen3-14b.
2025-08-01 20:33:04.271 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 20:33:04.273 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 20:41:06.340 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 20:41:06.349 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 20:41:07.497 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 20:41:07.505 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 20:41:11.125 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 20:41:12.617 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 20:44:36.965 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '抖店_夕玲珠宝批发百货/vector_store/doubao-embedding-text-240715' from disk.
2025-08-01 20:44:37.524 | ERROR    | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:140 - Error in __cdecl faiss::FileIOReader::FileIOReader(const char *) at D:\a\faiss-wheels\faiss-wheels\faiss\faiss\impl\io.cpp:68: Error: 'f' failed: could not open data\knowledge_base\抖店_夕玲珠宝批发百货\vector_store\doubao-embedding-text-240715\index.faiss for reading: No such file or directory
2025-08-01 20:44:37.536 | ERROR    | chatchat.webui_pages.utils:to_json:233 - JSONDecodeError: API未能返回正确的JSON。Expecting value: line 1 column 1 (char 0)
2025-08-01 20:44:58.662 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '136b71076f65449b9fce9e90e043bed5/vector_store/doubao-embedding-text-240715' from disk.
2025-08-01 20:45:06.675 | INFO     | chatchat.server.knowledge_base.utils:file2docs:375 - RapidOCRDocLoader used for E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\knowledge_base\136b71076f65449b9fce9e90e043bed5\content\手镯话术 3.19.docx
2025-08-01 20:45:12.092 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:save:40 - 已将向量库 ('136b71076f65449b9fce9e90e043bed5', 'doubao-embedding-text-240715') 保存到磁盘
2025-08-01 20:46:35.612 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 20:46:35.612 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 20:46:43.753 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 20:46:43.760 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 20:46:47.249 | INFO     | __mp_main__:run_webui:79 - Webui MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 21:10:55.821 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '3931b8a1235e40ff918e844d295dcd90/vector_store/text-embedding-v4' from disk.
2025-08-01 21:11:07.394 | INFO     | chatchat.server.knowledge_base.utils:file2docs:375 - RapidOCRDocLoader used for E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\knowledge_base\3931b8a1235e40ff918e844d295dcd90\content\手镯话术 3.19.docx
2025-08-01 21:11:08.643 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:save:40 - 已将向量库 ('3931b8a1235e40ff918e844d295dcd90', 'text-embedding-v4') 保存到磁盘
2025-08-01 21:11:23.938 | ERROR    | chatchat.server.utils:get_Embeddings:365 - failed to create Embeddings for model: doubao-embedding-text-240715.
2025-08-01 21:11:23.938 | ERROR    | chatchat.server.utils:check_embed_model:379 - failed to access embed model 'doubao-embedding-text-240715': 'NoneType' object has no attribute 'embed_query'
2025-08-01 21:11:23.939 | ERROR    | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:273 - 错误文件: E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\chatchat\server\chat\kb_chat.py, 行号: 92
错误类型: ValueError, 错误信息: failed to access embed model 'doubao-embedding-text-240715': 'NoneType' object has no attribute 'embed_query'
2025-08-01 21:17:37.472 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 21:17:37.473 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 21:17:45.125 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 21:17:45.133 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 21:17:51.575 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 21:18:34.394 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 21:18:34.396 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 21:18:42.059 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 21:18:42.067 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 21:18:48.312 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 21:18:50.815 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '3931b8a1235e40ff918e844d295dcd90/vector_store/text-embedding-v4' from disk.
2025-08-01 21:19:21.270 | ERROR    | chatchat.server.utils:get_Embeddings:365 - failed to create Embeddings for model: doubao-embedding-text-240715.
2025-08-01 21:19:21.270 | ERROR    | chatchat.server.utils:check_embed_model:379 - failed to access embed model 'doubao-embedding-text-240715': 'NoneType' object has no attribute 'embed_query'
2025-08-01 21:19:21.271 | ERROR    | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:280 - 错误文件: E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\chatchat\server\chat\kb_chat.py, 行号: 96
错误类型: ValueError, 错误信息: failed to access embed model 'doubao-embedding-text-240715': 'NoneType' object has no attribute 'embed_query'
2025-08-01 21:20:29.290 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 21:20:29.290 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 21:20:36.512 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 21:20:36.519 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 21:20:42.522 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 21:21:03.647 | ERROR    | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:281 - 错误文件: E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\chatchat\server\chat\kb_chat.py, 行号: 88
错误类型: UnboundLocalError, 错误信息: local variable 'limit' referenced before assignment
2025-08-01 21:21:22.551 | ERROR    | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:281 - 错误文件: E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\chatchat\server\chat\kb_chat.py, 行号: 88
错误类型: UnboundLocalError, 错误信息: local variable 'limit' referenced before assignment
2025-08-01 21:21:46.444 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 21:21:46.444 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 21:21:53.560 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 21:21:53.568 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 21:21:59.554 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 21:22:10.879 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '3931b8a1235e40ff918e844d295dcd90/vector_store/text-embedding-v4' from disk.
2025-08-01 21:26:38.963 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 21:26:38.964 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 21:26:45.920 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 21:26:45.928 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 21:26:51.860 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 21:27:21.961 | ERROR    | chatchat.server.utils:get_Embeddings:365 - failed to create Embeddings for model: doubao-embedding-text-240715.
2025-08-01 21:27:21.962 | ERROR    | chatchat.server.utils:check_embed_model:379 - failed to access embed model 'doubao-embedding-text-240715': 'NoneType' object has no attribute 'embed_query'
2025-08-01 21:27:21.962 | ERROR    | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:280 - 错误文件: E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\chatchat\server\chat\kb_chat.py, 行号: 96
错误类型: ValueError, 错误信息: failed to access embed model 'doubao-embedding-text-240715': 'NoneType' object has no attribute 'embed_query'
2025-08-01 21:27:33.175 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '3931b8a1235e40ff918e844d295dcd90/vector_store/text-embedding-v4' from disk.
2025-08-01 21:33:41.155 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 21:33:41.156 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 21:33:48.477 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 21:33:48.484 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 21:33:54.483 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 21:34:51.814 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '3931b8a1235e40ff918e844d295dcd90/vector_store/text-embedding-v4' from disk.
2025-08-01 21:44:24.395 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 21:44:24.396 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 21:44:31.418 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 21:44:31.426 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 21:44:37.572 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 21:45:04.732 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '3931b8a1235e40ff918e844d295dcd90/vector_store/text-embedding-v4' from disk.
2025-08-01 21:51:00.985 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 21:51:00.985 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 21:51:08.372 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 21:51:08.380 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 21:51:14.590 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 21:51:25.744 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '3931b8a1235e40ff918e844d295dcd90/vector_store/text-embedding-v4' from disk.
2025-08-01 21:51:26.641 | ERROR    | chatchat.server.chat.kb_chat:knowledge_base_chat_iterator:368 - 错误文件: E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\chatchat\server\chat\kb_chat.py, 行号: 288
错误类型: AttributeError, 错误信息: 'ChatMessagePromptTemplate' object has no attribute 'type'
2025-08-01 21:52:41.243 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 21:52:41.243 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 21:52:48.585 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 21:52:48.592 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 21:52:54.593 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 21:53:03.827 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '3931b8a1235e40ff918e844d295dcd90/vector_store/text-embedding-v4' from disk.
2025-08-01 22:00:31.796 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 22:00:31.796 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 22:00:39.061 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 22:00:39.069 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 22:00:45.044 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 22:01:03.621 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '3931b8a1235e40ff918e844d295dcd90/vector_store/text-embedding-v4' from disk.
2025-08-01 22:13:12.371 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 22:13:12.373 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 22:13:19.467 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 22:13:19.474 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 22:13:25.386 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 22:13:32.429 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '3931b8a1235e40ff918e844d295dcd90/vector_store/text-embedding-v4' from disk.
2025-08-01 22:19:51.143 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 22:19:51.143 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 22:19:58.557 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 22:19:58.564 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 22:20:04.779 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
2025-08-01 22:20:23.012 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '3931b8a1235e40ff918e844d295dcd90/vector_store/text-embedding-v4' from disk.
2025-08-01 22:22:17.439 | WARNING  | __main__:start_main_server:314 - Sending SIGKILL to %s
2025-08-01 22:22:17.439 | INFO     | __main__:start_main_server:325 - Process status: %s
2025-08-01 22:22:21.076 | INFO     | __main__:start_main_server:256 - 正在启动服务：
2025-08-01 22:22:21.078 | INFO     | __main__:start_main_server:257 - 如需查看 llm_api 日志，请前往 E:\kefu\Langchain-Chatchat-master\libs\chatchat-server\data\logs
2025-08-01 22:22:24.236 | INFO     | __mp_main__:run_api_server:54 - Api MODEL_PLATFORMS: [PlatformConfig(platform_name='xinference', platform_type='xinference', api_base_url='http://127.0.0.1:9997/v1', api_key='EMPTY', api_proxy='', api_concurrencies=5, auto_detect_model=True, llm_models=[], embed_models=[], text2image_models=[], image2text_models=[], rerank_models=[], speech2text_models=[], text2speech_models=[])]
