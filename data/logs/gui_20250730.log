2025-07-30 17:52:08 - INFO - 正在启动服务...
2025-07-30 17:52:08 - INFO - 服务已启动
2025-07-30 17:52:15 - INFO - API-ERR: pydantic\_internal\_fields.py:160: UserWarning: Field "model_type" has conflict with protected namespace "model_".
2025-07-30 17:52:15 - INFO - API-ERR: 
2025-07-30 17:52:15 - INFO - API-ERR: You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
2025-07-30 17:52:15 - INFO - API-ERR: pydantic\_internal\_fields.py:160: UserWarning: Field "model_id" has conflict with protected namespace "model_".
2025-07-30 17:52:15 - INFO - API-ERR: 
2025-07-30 17:52:15 - INFO - API-ERR: You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
2025-07-30 17:52:15 - INFO - API-ERR: pydantic\_internal\_fields.py:160: UserWarning: Field "model_name" has conflict with protected namespace "model_".
2025-07-30 17:52:15 - INFO - API-ERR: 
2025-07-30 17:52:15 - INFO - API-ERR: You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
2025-07-30 17:52:15 - INFO - API-ERR: pydantic\_internal\_fields.py:160: UserWarning: Field "model_code" has conflict with protected namespace "model_".
2025-07-30 17:52:15 - INFO - API-ERR: 
2025-07-30 17:52:15 - INFO - API-ERR: You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
2025-07-30 17:52:16 - INFO - API-ERR: langchain\_api\module_import.py:87: LangChainDeprecationWarning: Importing GuardrailsOutputParser from langchain.output_parsers is deprecated. Please replace the import with the following:
2025-07-30 17:52:16 - INFO - API-ERR: from langchain_community.output_parsers.rail_parser import GuardrailsOutputParser
2025-07-30 17:52:17 - INFO - API-ERR: pydantic\_internal\_fields.py:160: UserWarning: Field "model_name" has conflict with protected namespace "model_".
2025-07-30 17:52:17 - INFO - API-ERR: 
2025-07-30 17:52:17 - INFO - API-ERR: You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
2025-07-30 17:52:17 - INFO - API-ERR: pydantic\_internal\_fields.py:160: UserWarning: Field "model_type" has conflict with protected namespace "model_".
2025-07-30 17:52:17 - INFO - API-ERR: 
2025-07-30 17:52:17 - INFO - API-ERR: You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
2025-07-30 17:52:17 - INFO - API-ERR: pydantic\_internal\_fields.py:160: UserWarning: Field "model_id" has conflict with protected namespace "model_".
2025-07-30 17:52:17 - INFO - API-ERR: 
2025-07-30 17:52:17 - INFO - API-ERR: You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
2025-07-30 17:52:17 - INFO - API-ERR: pydantic\_internal\_fields.py:160: UserWarning: Field "model_code" has conflict with protected namespace "model_".
2025-07-30 17:52:17 - INFO - API-ERR: 
2025-07-30 17:52:17 - INFO - API-ERR: You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
2025-07-30 17:52:17 - INFO - API-ERR: INFO:     Started server process [12772]
2025-07-30 17:52:17 - INFO - API-ERR: INFO:     Waiting for application startup.
2025-07-30 17:52:17 - INFO - API-ERR: INFO:     Application startup complete.
2025-07-30 17:52:17 - INFO - API-ERR: INFO:     Uvicorn running on http://0.0.0.0:7861 (Press CTRL+C to quit)
2025-07-30 17:52:22 - INFO - WebUI-OUT: 
2025-07-30 17:52:22 - INFO - WebUI-OUT: You can now view your Streamlit app in your browser.
2025-07-30 17:52:22 - INFO - WebUI-OUT: 
2025-07-30 17:52:22 - INFO - WebUI-OUT: Local URL: http://localhost:8501
2025-07-30 17:52:22 - INFO - WebUI-OUT: Network URL: http://*************:8501
2025-07-30 17:52:22 - INFO - WebUI-OUT: 
2025-07-30 17:53:26 - INFO - API-OUT: INFO:     127.0.0.1:55247 - "GET /knowledge_base/list_knowledge_bases HTTP/1.1" 200 OK
2025-07-30 17:53:28 - INFO - API-OUT: INFO:     127.0.0.1:55249 - "POST /model/get_platform_info HTTP/1.1" 200 OK
2025-07-30 17:53:29 - INFO - API-OUT: INFO:     127.0.0.1:55249 - "POST /model/get_platform_model HTTP/1.1" 200 OK
2025-07-30 17:53:29 - INFO - API-OUT: INFO:     127.0.0.1:55250 - "POST /model/get_platform_info HTTP/1.1" 200 OK
2025-07-30 17:53:29 - INFO - API-OUT: INFO:     127.0.0.1:55250 - "POST /model/get_platform_model HTTP/1.1" 200 OK
2025-07-30 17:53:31 - INFO - API-OUT: INFO:     127.0.0.1:55251 - "POST /model/get_platform_info HTTP/1.1" 200 OK
2025-07-30 17:53:31 - INFO - API-OUT: INFO:     127.0.0.1:55252 - "POST /model/get_platform_info HTTP/1.1" 200 OK
2025-07-30 17:53:31 - INFO - API-OUT: INFO:     127.0.0.1:55252 - "POST /model/get_platform_model HTTP/1.1" 200 OK
2025-07-30 17:53:32 - INFO - API-OUT: INFO:     127.0.0.1:55253 - "POST /model/get_platform_info HTTP/1.1" 200 OK
2025-07-30 17:53:32 - INFO - API-OUT: INFO:     127.0.0.1:55253 - "POST /model/set_platform_api_key HTTP/1.1" 200 OK
2025-07-30 17:53:33 - INFO - API-OUT: INFO:     127.0.0.1:55255 - "POST /model/get_platform_info HTTP/1.1" 200 OK
2025-07-30 17:53:33 - INFO - API-OUT: INFO:     127.0.0.1:55255 - "POST /model/get_platform_model HTTP/1.1" 200 OK
2025-07-30 17:53:37 - INFO - API-OUT: INFO:     127.0.0.1:55256 - "POST /model/get_platform_info HTTP/1.1" 200 OK
2025-07-30 17:53:37 - INFO - API-OUT: INFO:     127.0.0.1:55256 - "POST /model/get_platform_model HTTP/1.1" 200 OK
2025-07-30 17:53:38 - INFO - API-OUT: INFO:     127.0.0.1:55257 - "POST /model/get_platform_info HTTP/1.1" 200 OK
2025-07-30 17:53:38 - INFO - API-OUT: INFO:     127.0.0.1:55257 - "POST /model/get_platform_model HTTP/1.1" 200 OK
2025-07-30 17:53:38 - INFO - API-OUT: INFO:     127.0.0.1:55258 - "POST /model/get_platform_info HTTP/1.1" 200 OK
2025-07-30 17:53:38 - INFO - API-OUT: INFO:     127.0.0.1:55258 - "POST /model/get_platform_model HTTP/1.1" 200 OK
2025-07-30 17:53:39 - INFO - API-OUT: INFO:     127.0.0.1:55258 - "POST /model/edit_platform_model HTTP/1.1" 200 OK
2025-07-30 17:53:39 - INFO - API-OUT: INFO:     127.0.0.1:55258 - "POST /model/edit_platform_model HTTP/1.1" 200 OK
2025-07-30 17:53:40 - INFO - API-OUT: INFO:     127.0.0.1:55259 - "POST /model/get_platform_info HTTP/1.1" 200 OK
2025-07-30 17:53:40 - INFO - API-OUT: INFO:     127.0.0.1:55259 - "POST /model/get_platform_model HTTP/1.1" 200 OK
2025-07-30 17:53:46 - INFO - API-ERR: 2025-07-30 17:53:46.464 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '1/vector_store/doubao-embedding-text-240715' from disk.
2025-07-30 17:53:46 - INFO - API-OUT: INFO:     127.0.0.1:55263 - "POST /knowledge_base/create_knowledge_base HTTP/1.1" 200 OK
2025-07-30 17:53:47 - INFO - WebUI-ERR: langchain\_api\module_import.py:87: LangChainDeprecationWarning: Importing GuardrailsOutputParser from langchain.output_parsers is deprecated. Please replace the import with the following:
2025-07-30 17:53:47 - INFO - WebUI-ERR: from langchain_community.output_parsers.rail_parser import GuardrailsOutputParser
2025-07-30 17:53:47 - INFO - WebUI-ERR: warnings.warn(
2025-07-30 17:54:03 - INFO - API-OUT: INFO:     127.0.0.1:55282 - "GET /knowledge_base/list_knowledge_bases HTTP/1.1" 200 OK
2025-07-30 17:54:07 - INFO - API-ERR: 2025-07-30 17:54:07.201 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '抖店_夕玲珠宝批发百货/vector_store/doubao-embedding-text-240715' from disk.
2025-07-30 17:54:07 - INFO - API-OUT: INFO:     127.0.0.1:55282 - "POST /business/add_shop HTTP/1.1" 200 OK
2025-07-30 17:54:14 - INFO - API-OUT: INFO:     127.0.0.1:55307 - "GET /knowledge_base/list_knowledge_bases HTTP/1.1" 200 OK
2025-07-30 17:54:23 - INFO - API-OUT: INFO:     127.0.0.1:55320 - "GET /knowledge_base/list_knowledge_bases HTTP/1.1" 200 OK
2025-07-30 17:54:23 - INFO - API-OUT: INFO:     127.0.0.1:55322 - "POST /server/get_prompt_template_list HTTP/1.1" 200 OK
2025-07-30 17:54:26 - INFO - API-OUT: INFO:     127.0.0.1:55322 - "POST /business/add_shop HTTP/1.1" 200 OK
2025-07-30 17:54:33 - INFO - API-OUT: INFO:     127.0.0.1:55340 - "POST /business/add_shop HTTP/1.1" 200 OK
2025-07-30 17:54:38 - INFO - API-ERR: 2025-07-30 17:54:38.994 | INFO     | chatchat.server.knowledge_base.utils:file2docs:375 - UnstructuredFileLoader used for D:\客服系统\server_customer_service\data\knowledge_base\抖店_夕玲珠宝批发百货\content\抖店_夕玲珠宝批发百货_产品.txt
2025-07-30 17:54:44 - INFO - API-ERR: 2025-07-30 17:54:44.055 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:save:40 - 已将向量库 ('抖店_夕玲珠宝批发百货', 'doubao-embedding-text-240715') 保存到磁盘
2025-07-30 17:54:44 - INFO - API-OUT: 文档切分示例：page_content='产品名称：【天然马料】白月光香槟金蜂蜜水一口糖冰透玉髓荔枝冻多样性发一 None None，产品类型：翡翠/和田玉/琥珀蜜蜡/其他玉石>玛瑙玉髓>手镯>undefined\n【天然马料】白月光香槟金蜂蜜水一口糖冰透玉髓荔枝冻多样性发一 None None 产品规格：{价格:129.9系列},描述：{价格:129.9系列},数量：10,单买价格：129.90,拼单价格：None,参考价格：None\n【天然马料】白月光香槟金蜂蜜水一口糖冰透玉髓荔枝冻多样性发一 None None 产品规格：{价格:99系列},描述：{价格:99系列},数量：10,单买价格：99.00,拼单价格：None,参考价格：None\n【天然马料】白月光香槟金蜂蜜水一口糖冰透玉髓荔枝冻多样性发一 None None 产品规格：{价格:88系列},描述：{价格:88系列},数量：10,单买价格：88.00,拼单价格：None,参考价格：None' metadata={'source': 'D:\\客服系统\\server_customer_service\\data\\knowledge_base\\抖店_夕玲珠宝批发百货\\content\\抖店_夕玲珠宝批发百货_产品.txt'}
2025-07-30 17:54:44 - INFO - API-OUT: INFO:     127.0.0.1:55347 - "POST /product/add_product HTTP/1.1" 200 OK
2025-07-30 17:54:59 - INFO - API-OUT: INFO:     127.0.0.1:55382 - "GET /knowledge_base/list_knowledge_bases HTTP/1.1" 200 OK
2025-07-30 17:55:05 - INFO - API-OUT: INFO:     127.0.0.1:55384 - "POST /knowledge_base/search_docs HTTP/1.1" 200 OK
2025-07-30 17:55:07 - INFO - API-OUT: INFO:     127.0.0.1:55385 - "GET /knowledge_base/list_knowledge_bases HTTP/1.1" 200 OK
2025-07-30 17:55:07 - INFO - API-OUT: INFO:     127.0.0.1:55386 - "GET /knowledge_base/list_knowledge_bases HTTP/1.1" 200 OK
2025-07-30 17:55:11 - INFO - API-OUT: INFO:     127.0.0.1:55390 - "GET /knowledge_base/list_knowledge_bases HTTP/1.1" 200 OK
2025-07-30 17:55:17 - INFO - API-OUT: INFO:     127.0.0.1:55391 - "GET /knowledge_base/list_knowledge_bases HTTP/1.1" 200 OK
2025-07-30 17:55:17 - INFO - API-OUT: INFO:     127.0.0.1:55393 - "POST /knowledge_base/local_kb/%E6%8A%96%E5%BA%97_%E5%A4%95%E7%8E%B2%E7%8F%A0%E5%AE%9D%E6%89%B9%E5%8F%91%E7%99%BE%E8%B4%A7/chat/completions HTTP/1.1" 200 OK
2025-07-30 17:55:18 - INFO - API-ERR: Building prefix dict from the default dictionary ...
2025-07-30 17:55:18 - INFO - API-ERR: Loading model from cache C:\Users\<USER>\AppData\Local\Temp\jieba.cache
2025-07-30 17:55:19 - INFO - API-ERR: Loading model cost 0.567 seconds.
2025-07-30 17:55:19 - INFO - API-ERR: Prefix dict has been built successfully.
2025-07-30 17:55:19 - INFO - API-ERR: langchain_core\_api\deprecation.py:119: LangChainDeprecationWarning: The method `BaseRetriever.get_relevant_documents` was deprecated in langchain-core 0.1.46 and will be removed in 0.3.0. Use invoke instead.
2025-07-30 18:17:43 - INFO - API-OUT: INFO:     127.0.0.1:55833 - "POST /business/add_shop HTTP/1.1" 200 OK
2025-07-30 18:18:05 - INFO - API-ERR: 2025-07-30 18:18:05.408 | INFO     | chatchat.server.knowledge_base.kb_cache.faiss_cache:load_vector_store:109 - loading vector store in '抖店1_1夕玲珠宝批发百货/vector_store/doubao-embedding-text-240715' from disk.
2025-07-30 18:18:05 - INFO - API-OUT: INFO:     127.0.0.1:55842 - "POST /business/add_shop HTTP/1.1" 200 OK
2025-07-30 18:30:59 - INFO - 正在退出应用程序...
2025-07-30 18:30:59 - INFO - 正在停止服务...
2025-07-30 18:30:59 - INFO - 进程 web_ui.exe (PID: 7384) 已停止
2025-07-30 18:30:59 - INFO - 进程 web_ui.exe (PID: 9924) 已停止
2025-07-30 18:30:59 - INFO - 进程 api_server.exe (PID: 12772) 已停止
2025-07-30 18:30:59 - INFO - 进程 api_server.exe (PID: 23452) 已停止
2025-07-30 18:30:59 - INFO - 所有服务已停止
2025-07-30 18:31:14 - INFO - 正在退出应用程序...
2025-07-30 18:31:14 - INFO - 服务未运行
