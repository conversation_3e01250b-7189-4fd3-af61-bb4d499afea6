from __future__ import annotations

import async<PERSON>, json
import sys
import uuid
from typing import AsyncIterable, List, Optional, Literal

from fastapi import Body, Request
from fastapi.concurrency import run_in_threadpool
from sse_starlette.sse import EventSourceResponse
from chatchat.server.chat.utils import SelfAsyncIteratorCallbackHandler
from langchain.prompts.chat import Chat<PERSON>romptTemplate

from chatchat.server.db.db_handler import add_chat_history
from chatchat.settings import Settings
from chatchat.server.agent.tools_factory.search_internet import search_engine
from chatchat.server.api_server.api_schemas import OpenAIChatOutput
from chatchat.server.chat.utils import History
from chatchat.server.knowledge_base.kb_service.base import KBServiceFactory
from chatchat.server.knowledge_base.kb_doc_api import search_docs, search_temp_docs
from chatchat.server.knowledge_base.utils import format_reference
from chatchat.server.utils import (wrap_done, get_ChatOpenAI, get_default_llm,
                                   BaseResponse, get_prompt_template, build_logger,
                                   check_embed_model, api_address, pref_prompt_template
                                   )

logger = build_logger()


async def kb_chat(query: str = Body(..., description="用户输入", examples=["你好"]),
                  mode: Literal["local_kb", "temp_kb", "search_engine"] = Body("local_kb", description="知识来源"),
                  kb_name: str = Body("",
                                      description="mode=local_kb时为知识库名称；temp_kb时为临时知识库ID，search_engine时为搜索引擎名称",
                                      examples=["samples"]),
                  top_k: int = Body(Settings.kb_settings.VECTOR_SEARCH_TOP_K, description="匹配向量数"),
                  score_threshold: float = Body(
                      Settings.kb_settings.SCORE_THRESHOLD,
                      description="知识库匹配相关度阈值，取值范围在0-1之间，SCORE越小，相关度越高，取到1相当于不筛选，建议设置在0.5左右",
                      ge=0,
                      le=2,
                  ),
                  history: List[History] = Body(
                      [],
                      description="历史对话",
                      examples=[[
                          {"role": "user",
                           "content": "我们来玩成语接龙，我先来，生龙活虎"},
                          {"role": "assistant",
                           "content": "虎头虎脑"}]]
                  ),
                  stream: bool = Body(True, description="流式输出"),
                  model: str = Body(get_default_llm(), description="LLM 模型名称。"),
                  temperature: float = Body(Settings.model_settings.TEMPERATURE, description="LLM 采样温度", ge=0.0,
                                            le=2.0),
                  max_tokens: Optional[int] = Body(
                      Settings.model_settings.MAX_TOKENS,
                      description="限制LLM生成Token数量，默认None代表模型最大值"
                  ),
                  prompt_name: str = Body(
                      "default",
                      description="使用的prompt模板名称(在prompt_settings.yaml中配置)"
                  ),
                  return_direct: bool = Body(False, description="直接返回检索结果，不送入 LLM"),
                  view_id: str = Body("", description="店铺id"),
                  request: Request = None,
                  ):

    # ----------------------------------------------------------------------------------------------------------------------------
    top_k = 3
    # ----------------------------------------------------------------------------------------------------------------------------

    if model == '' :
        model = get_default_llm()

    if mode == "local_kb":
        kb = KBServiceFactory.get_service_by_name(kb_name)
        if kb is None:
            return BaseResponse(code=404, msg=f"未找到知识库 {kb_name}")

    async def knowledge_base_chat_iterator() -> AsyncIterable[str]:
        answer = ""
        source_documents = []
        try:
            nonlocal history, prompt_name, max_tokens
            limit = 2
            # 限定10条数量,目前写死,以后根据需要是否可修改
            if not limit :
                limit = 10

            history = [History.from_data(h) for h in history[:limit]]

            if mode == "local_kb":
                kb = KBServiceFactory.get_service_by_name(kb_name)
                ok, msg = kb.check_embed_model()
                if not ok:
                    raise ValueError(msg)
                docs = await run_in_threadpool(search_docs,
                                               query=query,
                                               knowledge_base_name=kb_name,
                                               top_k=top_k,
                                               score_threshold=score_threshold,
                                               file_name="",
                                               metadata={})
                source_documents = format_reference(kb_name, docs, api_address(is_public=True))
            elif mode == "temp_kb":
                ok, msg = check_embed_model()
                if not ok:
                    raise ValueError(msg)
                docs = await run_in_threadpool(search_temp_docs,
                                               kb_name,
                                               query=query,
                                               top_k=top_k,
                                               score_threshold=score_threshold)
                source_documents = format_reference(kb_name, docs, api_address(is_public=True))
            elif mode == "search_engine":
                result = await run_in_threadpool(search_engine, query, top_k, kb_name)
                docs = [x.dict() for x in result.get("docs", [])]
                source_documents = [
                    f"""出处 [{i + 1}] [{d['metadata']['filename']}]({d['metadata']['source']}) \n\n{d['page_content']}\n\n"""
                    for i, d in enumerate(docs)]
            else:
                docs = []
                source_documents = []
            # import rich
            # rich.print(dict(
            #     mode=mode,
            #     query=query,
            #     knowledge_base_name=kb_name,
            #     top_k=top_k,
            #     score_threshold=score_threshold,
            # ))
            # rich.print(docs)
            if return_direct:
                yield OpenAIChatOutput(
                    id=f"chat{uuid.uuid4()}",
                    model=None,
                    object="chat.completion",
                    content="",
                    role="assistant",
                    finish_reason="stop",
                    docs=source_documents,
                ).model_dump_json()
                return

            callback = SelfAsyncIteratorCallbackHandler()
            callbacks = [callback]

            # Enable langchain-chatchat to support langfuse
            import os
            langfuse_secret_key = os.environ.get('LANGFUSE_SECRET_KEY')
            langfuse_public_key = os.environ.get('LANGFUSE_PUBLIC_KEY')
            langfuse_host = os.environ.get('LANGFUSE_HOST')
            if langfuse_secret_key and langfuse_public_key and langfuse_host:
                from langfuse import Langfuse
                from langfuse.callback import CallbackHandler
                langfuse_handler = CallbackHandler()
                callbacks.append(langfuse_handler)

            if max_tokens in [None, 0]:
                max_tokens = Settings.model_settings.MAX_TOKENS

            llm = get_ChatOpenAI(
                model_name=model,
                temperature=temperature,
                max_tokens=max_tokens,
                callbacks=callbacks,
            )
            # TODO： 视情况使用 API
            # # 加入reranker
            # if Settings.kb_settings.USE_RERANKER:
            #     reranker_model_path = get_model_path(Settings.kb_settings.RERANKER_MODEL)
            #     reranker_model = LangchainReranker(top_n=top_k,
            #                                     device=embedding_device(),
            #                                     max_length=Settings.kb_settings.RERANKER_MAX_LENGTH,
            #                                     model_name_or_path=reranker_model_path
            #                                     )
            #     print("-------------before rerank-----------------")
            #     print(docs)
            #     docs = reranker_model.compress_documents(documents=docs,
            #                                              query=query)
            #     print("------------after rerank------------------")
            #     print(docs)
            context = "\n\n".join([doc["page_content"] for doc in docs])

            # 链路顺序: system → 向量检索数据（作为 user/assistant） → 历史对话（user/assistant 交替） → 用户自定义模板（user）

            # # 添加系统模板
            system_prompt_template = pref_prompt_template()

            # ---------------------------------------------------------------------------------------------------

            # if not system_prompt_template or not system_prompt_template.strip():
            #     system_prompt_template = """【已知信息】{{context}}\n\n【问题】{{question}}"""
            # prompt_data = History(role="system", content=system_prompt_template).to_msg_template(False)
            #
            # # 历史记录
            # chain_lang = [prompt_data] + [i.to_msg_template() for i in history]
            #
            # # 用户模板
            # user_prompt_template = get_prompt_template("rag", prompt_name)
            # if user_prompt_template:
            #     user_prompt_template = f"""\n<user_instruction>\n'{user_prompt_template}\n</user_instruction>\n"""
            #     user_prompt_data = History(role="user", content=user_prompt_template).to_msg_template(False)
            #     chain_lang += [user_prompt_data]
            # chat_prompt = ChatPromptTemplate.from_messages(chain_lang)

            # ---------------------------------------------------------------------------------------------------

            prompt_template = system_prompt_template
            input_msg = History(role="user", content=prompt_template).to_msg_template(False)
            chat_prompt = ChatPromptTemplate.from_messages(
                [i.to_msg_template() for i in history] + [input_msg])
            # ---------------------------------------------------------------------------------------------------


            # 转换 拼接
            chain = chat_prompt | llm

            # Begin a task that runs in the background.
            task = asyncio.create_task(wrap_done(
                chain.ainvoke({"context": context, "question": query}),
                callback.done),
            )
            # ---------------------------------------------------------------------------------------------------
            print('知识库',context)
            # ---------------------------------------------------------------------------------------------------
            if len(source_documents) == 0:  # 没有找到相关文档
                source_documents.append(f"<span style='color:red'>未找到相关文档,该回答为大模型自身能力解答！</span>")

            if stream:
                # yield documents first
                ret = OpenAIChatOutput(
                    id=f"chat{uuid.uuid4()}",
                    object="chat.completion.chunk",
                    content="",
                    role="assistant",
                    model=model,
                    docs=source_documents,
                )
                yield ret.model_dump_json()

                async for token in callback.aiter():
                    answer += token
                    ret = OpenAIChatOutput(
                        id=f"chat{uuid.uuid4()}",
                        object="chat.completion.chunk",
                        content=token,
                        role="assistant",
                        model=model,
                    )
                    yield ret.model_dump_json()
                # 处理错误
                if not answer and callback.error:
                    ret = OpenAIChatOutput(
                        id=f"chat{uuid.uuid4()}",
                        object="chat.completion.chunk",
                        content=callback.error,
                        role="assistant",
                        model=model,
                    )
                    yield ret.model_dump_json()
            else:
                async for token in callback.aiter():
                    answer += token
                # 处理错误
                if not answer and callback.error:
                    answer = callback.error
                ret = OpenAIChatOutput(
                    id=f"chat{uuid.uuid4()}",
                    object="chat.completion",
                    content=answer,
                    role="assistant",
                    model=model,
                )
                yield ret.model_dump_json()

            await task
        except asyncio.exceptions.CancelledError:
            logger.warning("streaming progress has been interrupted by user.")
        except Exception as e:
            exc_type, exc_value, exc_traceback = sys.exc_info()
            # 获取最后一个栈帧（即错误发生的位置）
            last_traceback = exc_traceback
            while last_traceback.tb_next:
                last_traceback = last_traceback.tb_next

            file_name = last_traceback.tb_frame.f_code.co_filename  # 文件名
            line_number = last_traceback.tb_lineno  # 行号

            msg = f"错误文件: {file_name}, 行号: {line_number}\n错误类型: {exc_type.__name__}, 错误信息: {exc_value}"
            logger.error(msg)  # 打印错误信息以及完整追踪
            yield {"data": json.dumps({"msg": msg})}
        finally:
            # 确保无论如何都会尝试保存聊天记录
            try:
                str_history = json.dumps([h.dict() for h in history])
                str_source_docs = json.dumps(source_documents)
                add_chat_history(
                    add_type='system',
                    query=str(query),
                    mode=str(mode),
                    knowledge_base=str(kb_name),
                    history=str_history,
                    model=str(model),
                    prompt_name=str(prompt_name),
                    answer=str(answer),
                    source_documents=str_source_docs,
                    view_id=view_id,
                )
            except Exception as e:
                logger.error(f"Failed to save chat history: {e}")

    if stream:
        return EventSourceResponse(knowledge_base_chat_iterator())
    else:
        return await knowledge_base_chat_iterator().__anext__()
