# -*- mode: python ; coding: utf-8 -*-
import sys
sys.setrecursionlimit(sys.getrecursionlimit() * 5)

block_cipher = None

# 添加 Streamlit 的包元数据路径
def get_streamlit_metadata_path():
    import streamlit
    import os
    streamlit_path = os.path.dirname(streamlit.__file__)
    return (os.path.join(streamlit_path, '..', 'streamlit-*.dist-info'), 'streamlit')

a = Analysis(
    ['chatchat/start_web_ui.py'],  # 入口文件（根据实际情况调整）
    pathex=['.'],  # 项目根目录
    binaries=[],
    datas=[
    # 依赖
        ('E:/src/Langchain-Chatchat-master/.venv/Lib/site-packages/streamlit-1.34.0.dist-info', 'streamlit'),
        ('E:/src/Langchain-Chatchat-master/.venv/Lib/site-packages/streamlit/static', 'streamlit/static'),
        ("E:/src/Langchain-Chatchat-master/.venv/Lib/site-packages/streamlit/runtime", 'streamlit/runtime'),
        ('E:/src/Langchain-Chatchat-master/.venv/Lib/site-packages/streamlit_antd_components*', 'streamlit_antd_components'),
        ('E:/src/Langchain-Chatchat-master/.venv/Lib/site-packages/streamlit_antd_components-*.dist-info', 'streamlit_antd_components'),
        ('E:/src/Langchain-Chatchat-master/.venv/Lib/site-packages/streamlit_antd_components/frontend/build','streamlit_antd_components/frontend/build'),
        ('E:/src/Langchain-Chatchat-master/.venv/Lib/site-packages/httpx-*.dist-info', 'httpx'),
        ('E:/src/Langchain-Chatchat-master/.venv/Lib/site-packages/openai-*.dist-info', 'openai'),
        ('E:/src/Langchain-Chatchat-master/.venv/Lib/site-packages/fastapi-*.dist-info', 'fastapi'),
        ('E:/src/Langchain-Chatchat-master/.venv/Lib/site-packages/starlette-*.dist-info', 'starlette'),
        ('E:/src/Langchain-Chatchat-master/.venv/Lib/site-packages/pydantic-*.dist-info', 'pydantic'),
        ('E:/src/Langchain-Chatchat-master/.venv/Lib/site-packages/langchain*', 'langchain'),
        ('E:/src/Langchain-Chatchat-master/.venv/Lib/site-packages/langchain_core*', 'langchain_core'),
        ('E:/src/Langchain-Chatchat-master/.venv/Lib/site-packages/langchain_community*', 'langchain_community'),
        ('E:/src/Langchain-Chatchat-master/.venv/Lib/site-packages/loguru', 'loguru'),
        ('E:/src/Langchain-Chatchat-master/.venv/Lib/site-packages/memoization', 'memoization'),
        ('E:/src/Langchain-Chatchat-master/.venv/Lib/site-packages/streamlit_chatbox', 'streamlit_chatbox'),
        ('E:/src/Langchain-Chatchat-master/.venv/Lib/site-packages/streamlit_chatbox-*.dist-info', 'streamlit_chatbox'),
        ('E:/src/Langchain-Chatchat-master/.venv/Lib/site-packages/pandas', 'pandas'),
        ('E:/src/Langchain-Chatchat-master/.venv/Lib/site-packages/st_aggrid', 'st_aggrid'),
        ('E:/src/Langchain-Chatchat-master/.venv/Lib/site-packages/streamlit_extras', 'streamlit_extras'),
        ('E:/src/Langchain-Chatchat-master/.venv/Lib/site-packages/*.dist-info', '.'),
        ('E:/src/Langchain-Chatchat-master/.venv/Lib/site-packages/streamlit_feedback/frontend/build', 'streamlit_feedback/frontend/build'),
        ('E:/src/Langchain-Chatchat-master/.venv/Lib/site-packages/streamlit_feedback', 'streamlit_feedback'),
        ('E:/src/Langchain-Chatchat-master/.venv/Lib/site-packages/langchain', 'langchain'),
        ('E:/src/Langchain-Chatchat-master/.venv/Lib/site-packages/langchain_core', 'langchain_core'),
        ('E:/src/Langchain-Chatchat-master/.venv/Lib/site-packages/langchain_community', 'langchain_community'),
        ('E:/src/Langchain-Chatchat-master/.venv/Lib/site-packages/streamlit_paste_button', 'streamlit_paste_button'),
    # 添加所有需要的文件和目录
        ('chatchat', 'chatchat'),
        ('scripts', 'scripts'),
        ('langchain_chatchat', 'langchain_chatchat'),
        ('chatchat/webui.py', 'chatchat'),
        get_streamlit_metadata_path(),
    ],
    hiddenimports=[
        'requests', 'urllib3', 'chardet', 'idna',
        'streamlit',
        'streamlit.web',
        'streamlit.runtime',
        'streamlit.config',
        'streamlit.proto',
        'streamlit.runtime.scriptrunner',
        'chatchat',
        'httpx',
        'httpx._types',  # 可能需要添加子模块
        'httpx._client',  # 常用子模块
        'chatchat.settings',
        'importlib_metadata',  # 显式添加
        'streamlit_antd_components',
        'openai',
        'openai.api_resources',  # 按需添加
        'openai.error',          # 按需添加
        'fastapi',
        'streamlit_paste_button',
        'pydantic',      # fastapi 依赖
        'starlette',     # fastapi 依赖
        'fastapi.staticfiles',  # 如果使用了静态文件
        'fastapi.templating',   # 如果使用了模板渲染
        'langchain',  # 核心模块
        'langchain_core',  # 新版本可能拆分
        'langchain_community',  # 社区版依赖
        'langchain_openai',  # 如果用到 OpenAI
        'loguru',
        'loguru._logger',
        'memoization',
        'memoization.cached',
        'memoization.caching_algorithm_flags',
        'streamlit_chatbox',
        'streamlit_chatbox.chat',  # 如果有子模块也需要添加
        'pandas',
        'st_aggrid',
        'streamlit_extras',
        'pandas._libs',
        'pandas._libs.tslibs',
        'st_aggrid.grid_options_builder',
        'streamlit_extras.dataframe_explorer',
        'pyarrow',   # pandas 的 parquet 支持
        'streamlit_feedback',
        # langchain 核心模块
        'langchain',
        'langchain_core',
        'langchain_community',

        # 所有需要的子模块
        'langchain.docstore',
        'langchain.docstore.document',
        'langchain.docstore.wikipedia',
        'langchain.llms',
        'langchain.chains',
        'langchain.agents',
        'langchain.schema',

        # 其他可能需要的模块
        'langchain.text_splitter',
        'langchain.vectorstores',
    ],
    hookspath=['./hook'],
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,

)
pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='web_ui',  # 可执行文件名
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,  # 控制台模式（True=显示，False=隐藏）
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)