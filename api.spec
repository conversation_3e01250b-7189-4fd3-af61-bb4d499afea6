# -*- mode: python ; coding: utf-8 -*-
import sys
sys.setrecursionlimit(sys.getrecursionlimit() * 5)

block_cipher = None
a = Analysis(
    ['chatchat/server/api_server/server_app.py'],  # 入口文件（根据实际情况调整）
    pathex=['.'],  # 项目根目录
    binaries=[],
    datas=[
        # 添加所有需要的文件和目录
        ('chatchat', 'chatchat'),
        ('scripts', 'scripts'),
        ('langchain_chatchat', 'langchain_chatchat'),
        ('E:/src/Langchain-Chatchat-master/.venv/Lib/site-packages/unstructured/nlp/english-words.txt', 'unstructured/nlp'),
        # 添加 onnxruntime 的 DLL 文件（如果需要）
        ('E:/src/Langchain-Chatchat-master/.venv/Lib/site-packages/onnxruntime/capi/*.dll', 'onnxruntime/capi'),
        ('E:/src/Langchain-Chatchat-master/.venv/Lib/site-packages/rapidocr_onnxruntime/*', 'rapidocr_onnxruntime'),
    ],
    hiddenimports=[
        # 添加所有隐藏依赖
        'sqlalchemy',
        'click',
        'fastapi',
        'uvicorn',
        'multiprocessing',
        'langchain',
        'pydantic',
        'onnxruntime',
        'onnxruntime.capi',
        'rapidocr_onnxruntime',
    ],
    hookspath=[],
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)
pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='api_server',  # 输出可执行文件名
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,  # 是否显示控制台（调试时可设为 True，正式发布可设为 False）
    icon=None,  # 可指定 .ico 图标文件
    optimize=1,          # 启用优化
)