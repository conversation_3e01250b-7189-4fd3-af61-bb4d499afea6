import subprocess
import threading

import psutil
import pystray
from PIL import Image
import os
import signal

from log import setup_logger

# 全局变量存储进程和状态
api_process = None
web_ui_process = None
running = False
output_threads = []
logger = setup_logger("gui")

# pyinstaller  --noconsole --onefile --name "客服系统后台" gui.py
def create_image():
    # 创建一个简单的托盘图标
    image = Image.new('RGB', (64, 64), 'white')
    return image


def read_output(stream, stream_type):
    for line in stream:
        logger.info(f"{stream_type}: {line.strip()}")
        print(f"{stream_type}: {line.strip()}")


def start_services(icon=None):
    global api_process, web_ui_process, running, output_threads

    if running:
        logger.info("服务已经在运行中")
        print("服务已经在运行中")
        return

    logger.info("正在启动服务...")
    print("正在启动服务...")

    try:
        # 启动api_server
        api_process = subprocess.Popen(
            'api_server.exe',
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            shell=True
        )

        # 启动web_ui
        web_ui_process = subprocess.Popen(
            'web_ui.exe',
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            shell=True
        )

        # 为每个进程创建线程来读取输出
        for process, name in [(api_process, 'API'), (web_ui_process, 'WebUI')]:
            output_threads.append(threading.Thread(
                target=read_output, args=(process.stdout, f"{name}-OUT")
            ))
            output_threads.append(threading.Thread(
                target=read_output, args=(process.stderr, f"{name}-ERR")
            ))

        for t in output_threads:
            t.start()

        running = True
        logger.info("服务已启动")
        print("服务已启动")
        update_menu(icon)

    except Exception as e:
        logger.error(f"启动服务时出错: {e}", exc_info=True)
        print(f"启动服务时出错: {e}")


def stop_services(icon=None):
    global api_process, web_ui_process, running, output_threads

    if not running:
        logger.info("服务未运行")
        print("服务未运行")
        return

    logger.info("正在停止服务...")
    print("正在停止服务...")
    # 定义要终止的进程名列表
    process_names = ["api_server.exe", "web_ui.exe"]

    # 遍历所有进程
    for proc in psutil.process_iter(['pid', 'name']):
        try:
            # 检查进程名是否在要终止的列表中
            if proc.info['name'] in process_names:
                p = psutil.Process(proc.info['pid'])
                p.terminate()  # 尝试正常终止
                try:
                    p.wait(timeout=3)  # 等待进程终止
                    logger.info(f"进程 {proc.info['name']} (PID: {proc.info['pid']}) 已停止")
                    print(f"进程 {proc.info['name']} (PID: {proc.info['pid']}) 已停止")
                except psutil.TimeoutExpired:
                    p.kill()  # 如果超时，强制终止
                    print(f"强制终止进程 {proc.info['name']} (PID: {proc.info['pid']})")
                    logger.warning(f"强制终止进程 {proc.info['name']} (PID: {proc.info['pid']})")
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess) as e:
            print(f"处理进程时出错: {e}")
            logger.warning(f"处理进程时出错: {e}")

    # 重置状态
    api_process = None
    web_ui_process = None
    running = False
    output_threads = []
    logger.info("所有服务已停止")
    print("所有服务已停止")
    update_menu(icon)


def exit_app(icon=None):
    logger.info("正在退出应用程序...")
    print("正在退出应用程序...")
    stop_services()
    icon.stop()
    os._exit(0)


def update_menu(icon):
    # 根据运行状态更新菜单项
    icon.menu = pystray.Menu(
        pystray.MenuItem('开始', start_services, enabled=not running),
        pystray.MenuItem('停止', stop_services, enabled=running),
        pystray.MenuItem('退出', exit_app)
    )


def setup_tray_icon():
    # 创建系统托盘图标
    icon = pystray.Icon(
        "service_manager",
        icon=Image.open("icon.png") if os.path.exists("icon.png") else Image.new('RGB', (16, 16), 'white'),
        menu=pystray.Menu(
            pystray.MenuItem('开始', start_services, enabled=False),  # 初始禁用"开始"
            pystray.MenuItem('停止', stop_services, enabled=True),  # 初始启用"停止"
            pystray.MenuItem('退出', exit_app)
        ),
        title="后台客服系统"
    )

    # 处理Windows上的Ctrl+C
    if os.name == 'nt':
        def signal_handler(sig, frame):
            exit_app(icon)

        signal.signal(signal.SIGINT, signal_handler)

    return icon


def main():
    # 启动系统托盘图标
    icon = setup_tray_icon()

    # 在单独的线程中运行图标
    icon_thread = threading.Thread(target=icon.run)
    icon_thread.daemon = True
    icon_thread.start()

    # 程序启动时自动开始服务
    start_services(icon)

    # 使用事件对象实现阻塞等待
    stop_event = threading.Event()

    try:
        # 主线程阻塞在这里，直到收到停止信号
        stop_event.wait()
    except KeyboardInterrupt:
        exit_app(icon)
    finally:
        # 确保资源清理
        exit_app(icon)


if __name__ == "__main__":
    main()
