[tool.poetry]
name = "langchain-chatchat"
version = "*******"
description = "Langchain-Chatchat (formerly langchain-ChatGLM), local knowledge based LLM (like ChatGLM, Qwen and Llama) RAG and Agent app with langchain"
authors = ["chatchat"]
readme = "README.md"
packages = [
    {include = "chatchat"},
    {include = "langchain_chatchat"}
]

[tool.poetry.scripts]
chatchat = 'chatchat.cli:main'

[tool.poetry.dependencies]
python = ">=3.8.1,<3.12,!=3.9.7"
langchain = { version = "0.1.17", python = ">=3.8.1,<3.12,!=3.9.7" }
langchainhub = "0.1.14"
langchain-community = "0.0.36"
langchain-openai = { version = "0.0.6", python = ">=3.8.1,<3.12,!=3.9.7" }
langchain-experimental = "0.0.58"
fastapi = "~0.109.2"
sse_starlette = "~1.8.2"
nltk = "~3.8.1"
uvicorn = ">=0.27.0.post1"
unstructured = "~0.11.0"
python-magic-bin = {version = "*", platform = "win32"}
SQLAlchemy = "~2.0.25"
faiss-cpu = "~1.7.4"
jieba = "0.42.1"
rank_bm25 = "0.2.2"
openpyxl = "3.1.4"
networkx = "3.1"
opencv-python = "~*********"
PyMuPDF = "~1.23.16"
rapidocr_onnxruntime = "~1.3.8"
requests = "~2.31.0"
pathlib = "~1.0.1"
pyjwt = "~2.8.0"
elasticsearch = "*"
numexpr = ">=1" #test
strsimpy = ">=0.2.1"
markdownify = ">=0.11.6"
tqdm = ">=4.66.1"
websockets = ">=12.0"
numpy = "~1.24.4"
pandas = "~1.3.0" # test
pydantic = "~2.7.4"
httpx = {version = "0.27.2", extras = ["brotli", "http2", "socks"]}
python-multipart = "0.0.9"
# webui
streamlit = "1.34.0"
streamlit-option-menu = "0.3.12"
streamlit-antd-components = "0.3.1"
streamlit-chatbox = "1.1.12.post4"
streamlit-modal = "0.1.0"
streamlit-aggrid = "1.0.5"
streamlit-extras = "0.4.2"
htbuilder = "0.6.2"
xinference_client = { version = "^0.13.0", optional = true }
zhipuai = { version = "^2.1.0", optional = true }
pymysql = "^1.1.0"
memoization = "0.4.0"
pydantic_settings = "2.3.4"
ruamel_yaml = "0.18.6"
loguru = "^0.7.2"
streamlit-paste-button = "0.1.2"
python-docx = "1.1.2"

[tool.poetry.extras]
xinference = ["xinference_client"]
zhipuai = ["zhipuai"]
ollama = ["ollama"]

# An extra used to be able to add extended testing.
# Please use new-line on formatting to make it easier to add new packages without
# merge-conflicts
extended_testing = [
    "aleph-alpha-client",
    "aiosqlite",
    "assemblyai",
    "beautifulsoup4",
    "bibtexparser",
    "cassio",
    "datasets",
    "google-cloud-documentai",
    "esprima",
    "pdfminer-six",
    "pgvector",
    "pypdf",
    "pypdfium2",
    "lxml",
    "atlassian-python-api",
    "mwparserfromhell",
    "mwxml",
    "msal",
    "telethon",
    "psychicapi",
    "gql",
    "gradientai",
    "requests-toolbelt",
    "html2text",
    "py-trello",
    "scikit-learn",
    "pyspark",
    "sympy",
    "rapidfuzz",
    "jsonschema",
    "geopandas",
    "gitpython",
    "newspaper3k",
    "nvidia-riva-client",
    "feedparser",
    "xata",
    "xmltodict",
    "openapi-pydantic",
    "arxiv",
    "sqlite-vss",
    "motor",
    "timescale-vector",
    "anthropic",
    "upstash-redis",
    "rspace_client",
    "fireworks-ai",
    "javelin-sdk",
    "hologres-vector",
    "praw",
    "databricks-vectorsearch",
    "cloudpickle",
    "dgml-utils",
    "cohere",
    "tree-sitter",
    "tree-sitter-languages",
    "azure-ai-documentintelligence",
    "oracle-ads",
    "elasticsearch",
    "hdbcli",
    "oci",
    "rdflib",
    "tidb-vector",
    "cloudpickle",
    "friendli-client"
]

[tool.poetry.group.test]
optional = true

[tool.poetry.group.test.dependencies]
# The only dependencies that should be added are
# dependencies used for running tests (e.g., pytest, freezegun, response).
# Any dependencies that do not meet that criteria will be removed.
pytest = "^7.3.0"
pytest-cov = "^4.0.0"
pytest-dotenv = "^0.5.2"
duckdb-engine = "^0.9.2"
pytest-watcher = "^0.2.6"
freezegun = "^1.2.2"
responses = "^0.22.0"
pytest-asyncio = "^0.23.2"
lark = "^1.1.5"
pytest-mock = "^3.10.0"
pytest-socket = "^0.6.0"
syrupy = "^4.0.2"
requests-mock = "^1.11.0"


[tool.poetry.group.lint]
optional = true

[tool.poetry.group.lint.dependencies]
ruff = "^0.1.5"


[tool.poetry.group.codespell]
optional = true

[tool.poetry.group.codespell.dependencies]
codespell = "^2.2.0"


[tool.poetry.group.dev]
optional = true

[tool.poetry.group.dev.dependencies]
jupyter = "^1.0.0"
setuptools = "^67.6.1"



[tool.ruff]
exclude = [
    "tests/examples/non-utf8-encoding.py",
    "tests/integration_tests/examples/non-utf8-encoding.py",
]

[tool.ruff.lint]
select = [
    "E",  # pycodestyle
    "F",  # pyflakes
    "I",  # isort
    "T201", # print
]

[tool.mypy]
ignore_missing_imports = "True"
disallow_untyped_defs = "True"
exclude = ["notebooks", "examples", "example_data"]

[tool.coverage.run]
omit = [
    "tests/*",
]

[build-system]
requires = ["poetry-core>=1.0.0", "poetry-plugin-pypi-mirror==0.4.2"]
build-backend = "poetry.core.masonry.api"

[tool.pytest.ini_options]
# --strict-markers will raise errors on unknown marks.
# https://docs.pytest.org/en/7.1.x/how-to/mark.html#raising-errors-on-unknown-marks
#
# https://docs.pytest.org/en/7.1.x/reference/reference.html
# --strict-config       any warnings encountered while parsing the `pytest`
#                       section of the configuration file raise errors.
#
# https://github.com/tophat/syrupy
# --snapshot-warn-unused    Prints a warning on unused snapshots rather than fail the test suite.
addopts = "--strict-markers --strict-config --durations=5 --snapshot-warn-unused -svv"
# Registering custom markers.
# https://docs.pytest.org/en/7.1.x/example/markers.html#registering-markers
markers = [
    "requires: mark tests as requiring a specific library",
    "scheduled: mark tests to run in scheduled testing",
    "compile: mark placeholder test used to compile integration tests without running them"
]
asyncio_mode = "auto"


[tool.codespell]
skip = '.git,*.pdf,*.svg,*.pdf,*.yaml,*.ipynb,poetry.lock,*.min.js,*.css,package-lock.json,example_data,_dist,examples,*.trig,*.json,*.md,*.html,*.txt,*.csv'
# Ignore latin etc
ignore-regex = '.*(Stati Uniti|Tense=Pres).*'
# whats is a typo but used frequently in queries so kept as is
# aapply - async apply
# unsecure - typo but part of API, decided to not bother for now
ignore-words-list = 'momento,collison,ned,foor,reworkd,parth,whats,aapply,mysogyny,unsecure,damon,crate,aadd,symbl,precesses,accademia,nin'

[tool.poetry.plugins.dotenv]
ignore = "false"
dotenv = "dotenv:plugin"


[[tool.poetry.source]]
name = "huaweicloud"
url = "https://repo.huaweicloud.com/repository/pypi/simple/"
priority = "primary"